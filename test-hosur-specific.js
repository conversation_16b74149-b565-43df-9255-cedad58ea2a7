// Test script specifically for Hosur filtering issue
const axios = require('axios');

async function testHosurFiltering() {
  const baseUrl = 'http://localhost:3000'; // Adjust as needed
  const clientId = 17; // Adjust as needed
  
  try {
    console.log('=== TESTING HOSUR SPECIFIC FILTERING ===');
    
    // Test the exact payload you mentioned
    const hosurPayload = {
      framework: [],
      year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
      indicatorId: [0],
      userId: 17,
      locationFilter: {
        tier1_id: 103,  // India
        tier2_id: 130,  // Hosur (your actual ID)
        tier3_id: 0     // Should show Hosur + all BUs
      }
    };

    console.log('Testing payload:', JSON.stringify(hosurPayload, null, 2));
    
    const response = await axios.post(
      `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
      hosurPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers as needed
        }
      }
    );

    console.log(`\n=== RESULTS ===`);
    console.log(`Total items returned: ${response.data.length}`);
    
    if (response.data.length === 0) {
      console.log('❌ NO DATA RETURNED - This confirms the issue!');
      return;
    }

    // Group by level and locationId
    const groupedData = {};
    response.data.forEach(item => {
      const key = `Level ${item.level}`;
      if (!groupedData[key]) {
        groupedData[key] = {};
      }
      
      const locationKey = `LocationId ${item.locationId}`;
      if (!groupedData[key][locationKey]) {
        groupedData[key][locationKey] = {
          entity: item.entity || 'Unknown',
          count: 0,
          items: []
        };
      }
      groupedData[key][locationKey].count++;
      groupedData[key][locationKey].items.push(item);
    });

    console.log('\n=== DATA BREAKDOWN ===');
    Object.keys(groupedData).sort().forEach(level => {
      console.log(`\n${level}:`);
      Object.keys(groupedData[level]).sort((a, b) => {
        const idA = parseInt(a.split(' ')[1]);
        const idB = parseInt(b.split(' ')[1]);
        return idA - idB;
      }).forEach(locationKey => {
        const info = groupedData[level][locationKey];
        console.log(`  ${locationKey}: ${info.entity} (${info.count} items)`);
      });
    });

    // Check specifically for Hosur data
    const hosurLevel2 = response.data.filter(item => item.level === 2 && item.locationId === 130);
    const level3Data = response.data.filter(item => item.level === 3);

    console.log('\n=== HOSUR ANALYSIS ===');
    console.log(`Hosur city data (Level 2, LocationId 130): ${hosurLevel2.length} items`);
    console.log(`Level 3 data (BUs): ${level3Data.length} items`);

    if (hosurLevel2.length === 0) {
      console.log('❌ ISSUE: No Hosur city data found');
    } else {
      console.log('✅ Hosur city data found');
    }

    if (level3Data.length === 0) {
      console.log('❌ ISSUE: No Business Unit data found');
    } else {
      console.log('✅ Business Unit data found:');
      level3Data.forEach(item => {
        console.log(`  - LocationId ${item.locationId}: ${item.entity || 'Unknown'}`);
      });
    }

    // Test with role-based filtering to see what data is available
    console.log('\n=== TESTING ROLE-BASED FILTERING FOR COMPARISON ===');
    
    const roleBasedPayload = {
      framework: [],
      year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
      indicatorId: [0],
      userId: 17,
      locationFilter: {
        tier1_id: null,
        tier2_id: null,
        tier3_id: null
      }
    };

    const roleResponse = await axios.post(
      `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
      roleBasedPayload,
      {
        headers: {
          'Content-Type': 'application/json',
        }
      }
    );

    console.log(`Role-based filtering returned: ${roleResponse.data.length} items`);
    
    // Look for any Hosur-related data in role-based results
    const roleHosurData = roleResponse.data.filter(item => 
      (item.entity || '').toLowerCase().includes('hosur') || item.locationId === 130
    );
    
    console.log(`Hosur-related data in role-based results: ${roleHosurData.length} items`);
    if (roleHosurData.length > 0) {
      console.log('Hosur data found in role-based filtering:');
      roleHosurData.forEach(item => {
        console.log(`  Level ${item.level}, LocationId ${item.locationId}: ${item.entity}`);
      });
    }

  } catch (error) {
    console.error('Error testing Hosur filtering:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testHosurFiltering();
