// Test for Hosur issue with improved getValidTierIds logic
const axios = require('axios');

async function testImprovedHosur() {
  const baseUrl = 'http://localhost:3000';
  const clientId = 17;
  
  console.log('=== TESTING HOSUR WITH IMPROVED getValidTierIds LOGIC ===');
  
  // Test the exact scenario you mentioned
  const payload = {
    framework: [],
    year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
    indicatorId: [0],
    userId: 17,
    locationFilter: {
      tier1_id: 103,  // India
      tier2_id: 130,  // Hosur
      tier3_id: 0     // Should show Hosur + all BUs
    }
  };

  try {
    console.log('Sending request with payload:');
    console.log(JSON.stringify(payload, null, 2));
    console.log('\nThis should now use the improved getValidTierIds logic...\n');
    
    const response = await axios.post(
      `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        }
      }
    );

    console.log(`\n=== RESULTS ===`);
    console.log(`Total items returned: ${response.data.length}`);
    
    if (response.data.length === 0) {
      console.log('❌ NO DATA RETURNED - Issue still exists!');
      console.log('Check the console logs for debugging information.');
    } else {
      console.log('✅ Data returned! Issue appears to be fixed.');
      
      // Group by level
      const byLevel = {};
      response.data.forEach(item => {
        const level = item.level;
        if (!byLevel[level]) byLevel[level] = [];
        byLevel[level].push(item);
      });
      
      Object.keys(byLevel).sort().forEach(level => {
        console.log(`\nLevel ${level} (${byLevel[level].length} items):`);
        byLevel[level].forEach(item => {
          console.log(`  LocationId ${item.locationId}: ${item.entity || 'Unknown'}`);
        });
      });
      
      // Check specifically for Hosur and BUs
      const hosurData = response.data.filter(item => item.level === 2 && item.locationId === 130);
      const buData = response.data.filter(item => item.level === 3);
      
      console.log(`\n=== ANALYSIS ===`);
      console.log(`Hosur city data (Level 2, LocationId 130): ${hosurData.length} items`);
      console.log(`Business Unit data (Level 3): ${buData.length} items`);
      
      if (hosurData.length > 0 && buData.length > 0) {
        console.log('✅ SUCCESS: Both Hosur city and BU data found!');
      } else if (hosurData.length > 0) {
        console.log('⚠️  PARTIAL: Hosur city found but no BUs');
      } else {
        console.log('❌ ISSUE: No Hosur city data found');
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testImprovedHosur();
