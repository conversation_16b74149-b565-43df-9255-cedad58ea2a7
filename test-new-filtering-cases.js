// Test for the new filtering cases in getEnterpriseRawDataNew
const axios = require('axios');

async function testNewFilteringCases() {
  const baseUrl = 'http://localhost:3000';
  const clientId = 17;

  console.log('=== TESTING UNIFIED FILTERING LOGIC ===');
  console.log('Testing getEnterpriseRawDataNew endpoint with unified role access filtering\n');

  // Test cases for the new filtering logic
  const testCases = [
    {
      name: "Role-based filtering (no location filter)",
      description: "No locationFilter provided - should show all data based on user's role assignments",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17
        // No locationFilter = show all accessible data
      },
      expectedBehavior: "Shows all data from locations where user has admin privileges (hierarchical access)"
    },
    {
      name: "Role-based filtering (null location filter)",
      description: "LocationFilter with all null values - should show all data based on user's role assignments",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: null,
          tier2_id: null,
          tier3_id: null
        }
      },
      expectedBehavior: "Shows all data from locations where user has admin privileges (hierarchical access)"
    },
    {
      name: "Corporate level filtering",
      description: "Corporate level access (tier1_id = 0)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 0,
          tier2_id: null,
          tier3_id: null
        }
      },
      expectedBehavior: "Shows all data if user has corporate access"
    },
    {
      name: "Country level filtering",
      description: "Country level filtering (India = 103, tier2_id = 0)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 0,    // All cities under India
          tier3_id: null
        }
      },
      expectedBehavior: "Shows India + all its cities + all BUs (if user has access to India)"
    },
    {
      name: "City level filtering",
      description: "City level filtering (Hosur = 130, tier3_id = 0)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 130,  // Hosur
          tier3_id: 0     // All BUs under Hosur
        }
      },
      expectedBehavior: "Shows Hosur + all its BUs (if user has access to Hosur)"
    },
    {
      name: "BU level filtering",
      description: "Business Unit level filtering (specific BU)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 130,  // Hosur
          tier3_id: 45    // Specific BU (example)
        }
      },
      expectedBehavior: "Shows only the specific BU (if user has access to it)"
    },
    {
      name: "Access denied test",
      description: "Request access to location user is not assigned to",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 999,  // Non-existent/unassigned country
          tier2_id: 0,
          tier3_id: null
        }
      },
      expectedBehavior: "Should return empty array (access denied)"
    }
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`--- Test ${i + 1}: ${testCase.name} ---`);
    console.log(`Description: ${testCase.description}`);
    console.log(`Expected: ${testCase.expectedBehavior}`);

    if (testCase.payload.locationFilter) {
      console.log(`LocationFilter:`, JSON.stringify(testCase.payload.locationFilter, null, 2));
    } else {
      console.log(`LocationFilter: Not provided (CASE 1)`);
    }

    try {
      const response = await axios.post(
        `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data-new`,
        testCase.payload,
        {
          headers: {
            'Content-Type': 'application/json',
          }
        }
      );

      console.log(`✅ SUCCESS: ${response.data.length} items returned`);

      if (response.data.length > 0) {
        // Analyze the results
        const byLevel = {};
        const byFormCategory = {};

        response.data.forEach(item => {
          // Group by level
          const level = item.level;
          if (!byLevel[level]) byLevel[level] = [];
          byLevel[level].push(item);

          // Group by form category
          const category = item.formCategory === 1 ? 'DCF' : item.formCategory === 2 ? 'SAP' : 'HR';
          if (!byFormCategory[category]) byFormCategory[category] = [];
          byFormCategory[category].push(item);
        });

        console.log(`Data breakdown:`);
        Object.keys(byLevel).sort().forEach(level => {
          console.log(`  Level ${level}: ${byLevel[level].length} items`);
        });

        Object.keys(byFormCategory).forEach(category => {
          console.log(`  ${category} data: ${byFormCategory[category].length} items`);
        });

        // Show some sample entities
        const entities = [...new Set(response.data.map(item => item.entity).filter(e => e && e !== 'NA'))];
        if (entities.length > 0) {
          console.log(`  Entities: ${entities.slice(0, 5).join(', ')}${entities.length > 5 ? '...' : ''}`);
        }
      } else {
        if (testCase.name.includes("Access denied")) {
          console.log(`✅ CORRECT: Access denied as expected`);
        } else {
          console.log(`⚠️  No data returned (could be access denied or no data available)`);
        }
      }

    } catch (error) {
      console.error(`❌ ERROR: ${error.message}`);
      if (error.response) {
        console.error(`Status: ${error.response.status}`);
        console.error(`Data:`, error.response.data);
      }
    }

    console.log(''); // Empty line for readability
  }

  console.log('=== TEST COMPLETE ===');
  console.log('Check the console logs for detailed filtering information:');
  console.log('- "🔍 CASE 1: Filtering based on user role assignments"');
  console.log('- "🎯 CASE 2: Filtering based on specific location request"');
  console.log('- "✅ ACCESS GRANTED" / "❌ ACCESS DENIED" messages');
}

testNewFilteringCases();
