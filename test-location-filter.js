// Test script to verify location filtering behavior
const axios = require('axios');

async function testLocationFiltering() {
  const baseUrl = 'http://localhost:3000'; // Adjust as needed
  const clientId = 17; // Adjust as needed

  // Test scenarios
  const testScenarios = [
    {
      name: "Test 1: Specific site (Mysore Plant)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 131,  // Mysore
          tier3_id: 234   // Mysore Plant
        }
      },
      expectedBehavior: "Should show only Mysore Plant data (Level 3, LocationId 234)"
    },
    {
      name: "Test 2: City level (Hosur)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 132   // Hosur (assuming this is the ID)
          // tier3_id not specified - should show <PERSON><PERSON><PERSON> + all its child entities
        }
      },
      expectedBehavior: "Should show Hosur data (Level 2) + all its child entities (Level 3)"
    },
    {
      name: "Test 3: City level without tier1_id (Hosur only)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier2_id: 132   // Hosur only
        }
      },
      expectedBehavior: "Should show Hosur data + all its child entities"
    }
  ];

  for (const scenario of testScenarios) {
    try {
      console.log(`\n${'='.repeat(60)}`);
      console.log(`${scenario.name}`);
      console.log(`Expected: ${scenario.expectedBehavior}`);
      console.log('Payload:', JSON.stringify(scenario.payload, null, 2));

      const response = await axios.post(
        `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
        scenario.payload,
        {
          headers: {
            'Content-Type': 'application/json',
            // Add authentication headers as needed
          }
        }
      );

      console.log('\n=== RESPONSE ===');
      console.log('Status:', response.status);
      console.log('Data count:', response.data.length);

      if (response.data.length === 0) {
        console.log('❌ ISSUE: No data returned - this might be the problem!');
        continue;
      }

      // Group by level and locationId to see what's being returned
      const groupedData = {};
      response.data.forEach(item => {
        const key = `Level ${item.level} - LocationId ${item.locationId}`;
        if (!groupedData[key]) {
          groupedData[key] = [];
        }
        groupedData[key].push(item.entity || 'Unknown Entity');
      });

      console.log('\n=== GROUPED DATA ===');
      Object.keys(groupedData).sort().forEach(key => {
        console.log(`${key}: ${groupedData[key].length} items`);
        console.log(`  Entities: ${[...new Set(groupedData[key])].join(', ')}`);
      });

      // Analyze the results based on the test scenario
      if (scenario.name.includes("Mysore Plant")) {
        const mysoreData = response.data.filter(item =>
          item.level === 2 && item.locationId === 131
        );
        const mysorePlantData = response.data.filter(item =>
          item.level === 3 && item.locationId === 234
        );

        console.log('\n=== ANALYSIS ===');
        console.log(`Mysore city data (Level 2, LocationId 131): ${mysoreData.length} items`);
        console.log(`Mysore Plant data (Level 3, LocationId 234): ${mysorePlantData.length} items`);

        if (mysoreData.length > 0) {
          console.log('❌ ISSUE: Mysore city data is included when only Mysore Plant should be shown');
        } else {
          console.log('✅ GOOD: Only Mysore Plant data is included');
        }
      } else if (scenario.name.includes("Hosur")) {
        const hosurData = response.data.filter(item =>
          item.level === 2 && item.locationId === 132
        );
        const hosurChildData = response.data.filter(item =>
          item.level === 3 && item.entity && item.entity.toLowerCase().includes('hosur')
        );

        console.log('\n=== ANALYSIS ===');
        console.log(`Hosur city data (Level 2, LocationId 132): ${hosurData.length} items`);
        console.log(`Hosur child entities (Level 3): ${hosurChildData.length} items`);

        if (hosurData.length === 0 && hosurChildData.length === 0) {
          console.log('❌ ISSUE: No Hosur data returned - this is the reported problem!');
        } else {
          console.log('✅ GOOD: Hosur data is included');
        }
      }

    } catch (error) {
      console.error(`Error in ${scenario.name}:`, error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }
  }
}

// Run the test
testLocationFiltering();
