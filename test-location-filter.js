// Test script to verify location filtering behavior
const axios = require('axios');

async function testLocationFiltering() {
  const baseUrl = 'http://localhost:3000'; // Adjust as needed
  const clientId = 17; // Adjust as needed

  // Test scenarios based on user's reported issues
  const testScenarios = [
    {
      name: "Test 1: All null tiers (should use role-based filtering)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: null,
          tier2_id: null,
          tier3_id: null
        }
      },
      expectedBehavior: "Should show all data wherever user is assigned as Admin (Corporate/Country/City/BU)"
    },
    {
      name: "Test 2: Hosur only (should show Hosur + all BUs)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier2_id: 132   // Hosur (replace with actual ID)
        }
      },
      expectedBehavior: "Should show Hosur data + all its Business Units (Level 3)"
    },
    {
      name: "Test 3: Hosur with explicit null tier3_id",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 132,  // Hosur
          tier3_id: null  // Explicitly null - should show Hosur + all BUs
        }
      },
      expectedBehavior: "Should show Hosur data + all its Business Units under Hosur"
    },
    {
      name: "Test 4: Specific BU under Hosur",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 132,  // Hosur
          tier3_id: 235   // Hosur Plant 1 (replace with actual ID)
        }
      },
      expectedBehavior: "Should show only Hosur Plant 1 data"
    },
    {
      name: "Test 5: Original Mysore Plant issue",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 131,  // Mysore
          tier3_id: 234   // Mysore Plant
        }
      },
      expectedBehavior: "Should show only Mysore Plant data (not Mysore city data)"
    }
  ];

  for (const scenario of testScenarios) {
    try {
      console.log(`\n${'='.repeat(60)}`);
      console.log(`${scenario.name}`);
      console.log(`Expected: ${scenario.expectedBehavior}`);
      console.log('Payload:', JSON.stringify(scenario.payload, null, 2));

      const response = await axios.post(
        `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
        scenario.payload,
        {
          headers: {
            'Content-Type': 'application/json',
            // Add authentication headers as needed
          }
        }
      );

      console.log('\n=== RESPONSE ===');
      console.log('Status:', response.status);
      console.log('Data count:', response.data.length);

      if (response.data.length === 0) {
        console.log('❌ ISSUE: No data returned - this might be the problem!');
        continue;
      }

      // Group by level and locationId to see what's being returned
      const groupedData = {};
      response.data.forEach(item => {
        const key = `Level ${item.level} - LocationId ${item.locationId}`;
        if (!groupedData[key]) {
          groupedData[key] = [];
        }
        groupedData[key].push(item.entity || 'Unknown Entity');
      });

      console.log('\n=== GROUPED DATA ===');
      Object.keys(groupedData).sort().forEach(key => {
        console.log(`${key}: ${groupedData[key].length} items`);
        console.log(`  Entities: ${[...new Set(groupedData[key])].join(', ')}`);
      });

      // Analyze the results based on the test scenario
      console.log('\n=== ANALYSIS ===');

      if (scenario.name.includes("All null tiers")) {
        console.log('This should use role-based filtering and show all assigned data');
        console.log(`Total data returned: ${response.data.length} items`);

        if (response.data.length === 0) {
          console.log('❌ ISSUE: No data returned for role-based filtering');
        } else {
          console.log('✅ GOOD: Role-based filtering returned data');
        }

      } else if (scenario.name.includes("Hosur")) {
        const hosurData = response.data.filter(item =>
          item.level === 2 && item.locationId === 132
        );
        const level3Data = response.data.filter(item => item.level === 3);

        console.log(`Hosur city data (Level 2, LocationId 132): ${hosurData.length} items`);
        console.log(`Level 3 entities: ${level3Data.length} items`);

        if (level3Data.length > 0) {
          console.log('Level 3 entities found:');
          level3Data.forEach(item => {
            console.log(`  - LocationId ${item.locationId}: ${item.entity || 'Unknown'}`);
          });
        }

        if (scenario.name.includes("should show Hosur + all BUs")) {
          if (hosurData.length === 0) {
            console.log('❌ ISSUE: Hosur city data missing');
          }
          if (level3Data.length === 0) {
            console.log('❌ ISSUE: No Business Units under Hosur found');
          }
          if (hosurData.length > 0 && level3Data.length > 0) {
            console.log('✅ GOOD: Both Hosur and its BUs are included');
          }
        }

      } else if (scenario.name.includes("Mysore Plant")) {
        const mysoreData = response.data.filter(item =>
          item.level === 2 && item.locationId === 131
        );
        const mysorePlantData = response.data.filter(item =>
          item.level === 3 && item.locationId === 234
        );

        console.log(`Mysore city data (Level 2, LocationId 131): ${mysoreData.length} items`);
        console.log(`Mysore Plant data (Level 3, LocationId 234): ${mysorePlantData.length} items`);

        if (mysoreData.length > 0) {
          console.log('❌ ISSUE: Mysore city data is included when only Mysore Plant should be shown');
        } else {
          console.log('✅ GOOD: Only Mysore Plant data is included');
        }
      }

    } catch (error) {
      console.error(`Error in ${scenario.name}:`, error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }
  }
}

// Run the test
testLocationFiltering();
