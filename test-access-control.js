// Test for location access control validation
const axios = require('axios');

async function testLocationAccessControl() {
  const baseUrl = 'http://localhost:3000';
  const clientId = 17;
  
  console.log('=== TESTING LOCATION ACCESS CONTROL ===');
  
  // Test scenarios
  const testCases = [
    {
      name: "Valid Access - Hosur (assigned location)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 103,  // India
          tier2_id: 130,  // Hosur
          tier3_id: 0     // All BUs under Hosur
        }
      },
      expectedResult: "Should return data (if user has access to <PERSON><PERSON><PERSON>)"
    },
    {
      name: "Invalid Access - Random location (not assigned)",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 999,  // Non-existent country
          tier2_id: 999,  // Non-existent region
          tier3_id: 0
        }
      },
      expectedResult: "Should return empty array (access denied)"
    },
    {
      name: "Corporate Level Access",
      payload: {
        framework: [],
        year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
        indicatorId: [0],
        userId: 17,
        locationFilter: {
          tier1_id: 0,    // Corporate level
          tier2_id: null,
          tier3_id: null
        }
      },
      expectedResult: "Should return all data (corporate access)"
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- ${testCase.name} ---`);
    console.log('Payload:', JSON.stringify(testCase.payload.locationFilter, null, 2));
    console.log('Expected:', testCase.expectedResult);
    
    try {
      const response = await axios.post(
        `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
        testCase.payload,
        {
          headers: {
            'Content-Type': 'application/json',
          }
        }
      );

      console.log(`Result: ${response.data.length} items returned`);
      
      if (testCase.name.includes("Invalid Access")) {
        if (response.data.length === 0) {
          console.log('✅ CORRECT: Access denied as expected');
        } else {
          console.log('❌ ISSUE: Data returned when access should be denied');
        }
      } else {
        if (response.data.length > 0) {
          console.log('✅ CORRECT: Data returned as expected');
          
          // Show breakdown by level
          const byLevel = {};
          response.data.forEach(item => {
            const level = item.level;
            if (!byLevel[level]) byLevel[level] = [];
            byLevel[level].push(item);
          });
          
          Object.keys(byLevel).sort().forEach(level => {
            console.log(`  Level ${level}: ${byLevel[level].length} items`);
          });
        } else {
          console.log('⚠️  UNEXPECTED: No data returned (might be access denied or no data available)');
        }
      }

    } catch (error) {
      console.error('❌ Error:', error.message);
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Data:', error.response.data);
      }
    }
  }
  
  console.log('\n=== ACCESS CONTROL TEST COMPLETE ===');
  console.log('Check the console logs for access validation messages:');
  console.log('- "✅ ACCESS GRANTED" = User has access to requested location');
  console.log('- "❌ ACCESS DENIED" = User does not have access to requested location');
}

testLocationAccessControl();
