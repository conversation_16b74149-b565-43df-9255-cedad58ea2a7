// Simple test for Hosur issue
const axios = require('axios');

async function testSimpleHosur() {
  const baseUrl = 'http://localhost:3000';
  const clientId = 17;
  
  console.log('=== TESTING HOSUR ISSUE ===');
  
  // Test the exact scenario you mentioned
  const payload = {
    framework: [],
    year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
    indicatorId: [0],
    userId: 17,
    locationFilter: {
      tier1_id: 103,  // India
      tier2_id: 130,  // Hosur
      tier3_id: 0     // Should show Hosur + all BUs
    }
  };

  try {
    console.log('Sending request with payload:');
    console.log(JSON.stringify(payload, null, 2));
    
    const response = await axios.post(
      `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        }
      }
    );

    console.log(`\nResponse received: ${response.data.length} items`);
    
    if (response.data.length === 0) {
      console.log('❌ NO DATA RETURNED - Issue confirmed!');
    } else {
      console.log('✅ Data returned:');
      response.data.forEach(item => {
        console.log(`  Level ${item.level}, LocationId ${item.locationId}: ${item.entity || 'Unknown'}`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testSimpleHosur();
