# New Location Filtering Guide for getEnterpriseRawDataNew

## Overview

The `getEnterpriseRawDataNew` endpoint now supports two distinct filtering cases based on your requirements:

- **CASE 1**: Role-based filtering (show all data based on user assignments)
- **CASE 2**: Specific location filtering (show filtered data within user's access)

## CASE 1: Role-based Filtering

**When to use**: When you want to see all data from locations where you have admin privileges.

**How to request**:
```json
{
  "framework": [],
  "year": { "startMonth": "Jan-2024", "endMonth": "Dec-2024" },
  "indicatorId": [0],
  "userId": 17
  // No locationFilter OR locationFilter with all null values
}
```

**OR with explicit null values**:
```json
{
  "framework": [],
  "year": { "startMonth": "Jan-2024", "endMonth": "Dec-2024" },
  "indicatorId": [0],
  "userId": 17,
  "locationFilter": {
    "tier1_id": null,
    "tier2_id": null,
    "tier3_id": null
  }
}
```

**What you get**:
- If assigned at **Corporate level**: All data from all locations
- If assigned at **India level**: India + all its Cities + all its BUs
- If assigned at **Hosur level**: Hosur + all its BUs
- If assigned at **BU level**: Only that specific BU

## CASE 2: Specific Location Filtering

**When to use**: When you want to filter to show only specific location data (within your access).

### Corporate Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 0,
    "tier2_id": null,
    "tier3_id": null
  }
}
```
**Result**: All data (if you have corporate access)

### Country Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 103,  // India
    "tier2_id": 0,    // All cities under India
    "tier3_id": null
  }
}
```
**Result**: India + all its cities + all BUs under India

### City Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 103,  // India
    "tier2_id": 130,  // Hosur
    "tier3_id": 0     // All BUs under Hosur
  }
}
```
**Result**: Hosur + all its BUs

### Business Unit Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 103,  // India
    "tier2_id": 130,  // Hosur
    "tier3_id": 45    // Specific BU
  }
}
```
**Result**: Only the specific BU (ID: 45)

## Access Control Rules

### Hierarchical Access
- **Corporate assignment** (tier1_id=0): Can access everything
- **Country assignment** (tier1_id=103, tier2_id=0): Can access India + all cities + all BUs
- **City assignment** (tier1_id=103, tier2_id=130, tier3_id=0): Can access Hosur + all BUs
- **BU assignment** (tier1_id=103, tier2_id=130, tier3_id=45): Can access only BU 45

### Access Validation for CASE 2
The system validates that you can only filter at levels where you have admin privileges:

- ✅ **Allowed**: If you have India access, you can filter for India, Hosur, or specific BUs
- ❌ **Denied**: If you have Hosur access, you cannot filter for other cities or countries
- ❌ **Denied**: If you have BU access, you cannot filter for cities or countries

## Level Identification Rules

- **tier1_id = 0**: Corporate level
- **tier2_id = 0**: Country level (when tier1_id > 0)
- **tier3_id = 0**: City level (when tier1_id > 0 and tier2_id > 0)
- **tier3_id > 0**: Business Unit level (when all tiers > 0)

## Response Format

Both cases return the same data structure:
```json
[
  {
    "formCategory": 1,  // 1=DCF, 2=SAP, 3=HR
    "formId": "...",
    "value": 123.45,
    "entity": "Hosur Plant",
    "level": 2,
    "locationId": 130,
    "status": "Approved",
    // ... other fields
  }
]
```

## Testing

Use the provided test file to verify both cases:
```bash
node test-new-filtering-cases.js
```

## Console Logs

Look for these messages in the console to understand the filtering behavior:
- `🔍 CASE 1: Filtering based on user role assignments`
- `🎯 CASE 2: Filtering based on specific location request`
- `✅ ACCESS GRANTED: User has admin privileges for requested location`
- `❌ ACCESS DENIED: User does not have admin privileges for requested location`
