# Unified Filtering Guide for getEnterpriseRawDataNew

## Overview

The `getEnterpriseRawDataNew` endpoint now uses a unified role-based filtering system that handles all scenarios through a single `filterDataByRoleAccess` function:

- **Role-based access**: Shows data based on user's admin role assignments
- **Optional location filtering**: Allows specific location filtering within accessible areas
- **Access validation**: Ensures users can only access locations they're assigned to

## How the Unified Filtering Works

The system follows this pattern (similar to `checkRoleAccessByRoleIds`):

1. **Filter role assignments** based on userId and required roles (role 2 = admin)
2. **Check global access** (userId === adminId grants corporate access)
3. **Build accessible locations** from role assignments using hierarchical rules
4. **Apply location filter** if provided (with access validation)
5. **Filter data** based on final target locations

## Basic Usage (Show All Accessible Data)

**When to use**: When you want to see all data from locations where you have admin privileges.

**How to request**:
```json
{
  "framework": [],
  "year": { "startMonth": "Jan-2024", "endMonth": "Dec-2024" },
  "indicatorId": [0],
  "userId": 17
  // No locationFilter = show all accessible data
}
```

**OR with explicit null values**:
```json
{
  "framework": [],
  "year": { "startMonth": "Jan-2024", "endMonth": "Dec-2024" },
  "indicatorId": [0],
  "userId": 17,
  "locationFilter": {
    "tier1_id": null,
    "tier2_id": null,
    "tier3_id": null
  }
}
```

**What you get**:
- If assigned at **Corporate level**: All data from all locations
- If assigned at **India level**: India + all its Cities + all its BUs
- If assigned at **Hosur level**: Hosur + all its BUs
- If assigned at **BU level**: Only that specific BU

## Specific Location Filtering

**When to use**: When you want to filter to show only specific location data (within your access).

### Corporate Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 0,
    "tier2_id": null,
    "tier3_id": null
  }
}
```
**Result**: All data (if you have corporate access)

### Country Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 103,  // India
    "tier2_id": 0,    // All cities under India
    "tier3_id": null
  }
}
```
**Result**: India + all its cities + all BUs under India

### City Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 103,  // India
    "tier2_id": 130,  // Hosur
    "tier3_id": 0     // All BUs under Hosur
  }
}
```
**Result**: Hosur + all its BUs

### Business Unit Level Filter
```json
{
  "locationFilter": {
    "tier1_id": 103,  // India
    "tier2_id": 130,  // Hosur
    "tier3_id": 45    // Specific BU
  }
}
```
**Result**: Only the specific BU (ID: 45)

## Access Control Rules

### Hierarchical Access
- **Corporate assignment** (tier1_id=0): Can access everything
- **Country assignment** (tier1_id=103, tier2_id=0): Can access India + all cities + all BUs
- **City assignment** (tier1_id=103, tier2_id=130, tier3_id=0): Can access Hosur + all BUs
- **BU assignment** (tier1_id=103, tier2_id=130, tier3_id=45): Can access only BU 45

### Access Validation
The system validates that you can only filter at levels where you have admin privileges:

- ✅ **Allowed**: If you have India access, you can filter for India, Hosur, or specific BUs
- ❌ **Denied**: If you have Hosur access, you cannot filter for other cities or countries
- ❌ **Denied**: If you have BU access, you cannot filter for cities or countries

## Level Identification Rules

- **tier1_id = 0**: Corporate level
- **tier2_id = 0**: Country level (when tier1_id > 0)
- **tier3_id = 0**: City level (when tier1_id > 0 and tier2_id > 0)
- **tier3_id > 0**: Business Unit level (when all tiers > 0)

## Response Format

Both cases return the same data structure:
```json
[
  {
    "formCategory": 1,  // 1=DCF, 2=SAP, 3=HR
    "formId": "...",
    "value": 123.45,
    "entity": "Hosur Plant",
    "level": 2,
    "locationId": 130,
    "status": "Approved",
    // ... other fields
  }
]
```

## Testing

Use the provided test file to verify both cases:
```bash
node test-new-filtering-cases.js
```

## Console Logs

Look for these messages in the console to understand the filtering behavior:
- `=== UNIFIED ROLE ACCESS FILTERING ===`
- `🔍 Using all accessible locations (no specific filter)`
- `🎯 Applying specific location filter`
- `✅ ACCESS GRANTED: User has access to requested location`
- `❌ ACCESS DENIED: User does not have access to requested location`
- `✅ Global access granted (userId === adminId)`
