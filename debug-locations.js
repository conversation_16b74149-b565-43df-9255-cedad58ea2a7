// Debug script to understand location structure
const axios = require('axios');

async function debugLocationStructure() {
  const baseUrl = 'http://localhost:3000'; // Adjust as needed
  const clientId = 17; // Adjust as needed
  
  try {
    console.log('=== DEBUGGING LOCATION STRUCTURE ===');
    
    // First, let's get the location structure by calling with no filter
    const noFilterPayload = {
      framework: [],
      year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
      indicatorId: [0],
      userId: 17,
      locationFilter: {
        tier1_id: null,
        tier2_id: null,
        tier3_id: null
      }
    };

    console.log('Getting all data to understand location structure...');
    const response = await axios.post(
      `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
      noFilterPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers as needed
        }
      }
    );

    console.log(`\nTotal data items: ${response.data.length}`);
    
    // Group by level and locationId
    const locationMap = new Map();
    
    response.data.forEach(item => {
      const key = `Level ${item.level}`;
      if (!locationMap.has(key)) {
        locationMap.set(key, new Map());
      }
      
      const levelMap = locationMap.get(key);
      const locationKey = `LocationId ${item.locationId}`;
      if (!levelMap.has(locationKey)) {
        levelMap.set(locationKey, {
          entity: item.entity || 'Unknown',
          count: 0,
          sample: item
        });
      }
      levelMap.get(locationKey).count++;
    });

    console.log('\n=== LOCATION STRUCTURE ===');
    
    // Sort levels
    const sortedLevels = Array.from(locationMap.keys()).sort((a, b) => {
      const levelA = parseInt(a.split(' ')[1]);
      const levelB = parseInt(b.split(' ')[1]);
      return levelA - levelB;
    });

    sortedLevels.forEach(levelKey => {
      console.log(`\n${levelKey}:`);
      const levelMap = locationMap.get(levelKey);
      
      // Sort by locationId
      const sortedLocations = Array.from(levelMap.entries()).sort((a, b) => {
        const idA = parseInt(a[0].split(' ')[1]);
        const idB = parseInt(b[0].split(' ')[1]);
        return idA - idB;
      });
      
      sortedLocations.forEach(([locationKey, info]) => {
        console.log(`  ${locationKey}: ${info.entity} (${info.count} items)`);
      });
    });

    // Look for Hosur and Mysore specifically
    console.log('\n=== SEARCHING FOR HOSUR AND MYSORE ===');
    
    response.data.forEach(item => {
      const entity = (item.entity || '').toLowerCase();
      if (entity.includes('hosur') || entity.includes('mysore')) {
        console.log(`Found: Level ${item.level}, LocationId ${item.locationId}, Entity: ${item.entity}`);
      }
    });

    // Test specific Hosur filtering
    console.log('\n=== TESTING HOSUR FILTERING ===');
    
    // Find Hosur's actual ID from the data
    const hosurItems = response.data.filter(item => 
      (item.entity || '').toLowerCase().includes('hosur')
    );
    
    if (hosurItems.length > 0) {
      const hosurLevel2 = hosurItems.find(item => item.level === 2);
      if (hosurLevel2) {
        console.log(`Found Hosur at Level 2, LocationId: ${hosurLevel2.locationId}`);
        
        // Test filtering with this ID
        const hosurFilterPayload = {
          framework: [],
          year: { startMonth: "Jan-2024", endMonth: "Dec-2024" },
          indicatorId: [0],
          userId: 17,
          locationFilter: {
            tier2_id: hosurLevel2.locationId
          }
        };

        console.log(`Testing filter with tier2_id: ${hosurLevel2.locationId}`);
        
        try {
          const hosurResponse = await axios.post(
            `${baseUrl}/user-profiles/${clientId}/get-enterprise-raw-data`,
            hosurFilterPayload,
            {
              headers: {
                'Content-Type': 'application/json',
              }
            }
          );

          console.log(`Hosur filter result: ${hosurResponse.data.length} items`);
          
          if (hosurResponse.data.length > 0) {
            console.log('Items returned:');
            hosurResponse.data.forEach(item => {
              console.log(`  Level ${item.level}, LocationId ${item.locationId}: ${item.entity}`);
            });
          } else {
            console.log('❌ No data returned for Hosur filter - this confirms the issue!');
          }
          
        } catch (error) {
          console.error('Error testing Hosur filter:', error.message);
        }
      }
    }

  } catch (error) {
    console.error('Error debugging location structure:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the debug
debugLocationStructure();
