import {authenticate} from '@loopback/authentication';
import {
  UserCredentialsRepository,
  UserRepository
} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  property,
  repository,
  Where
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  Request,
  requestBody,
  Response,
  response,
  RestBindings,
  SchemaObject,
} from '@loopback/rest';
import {UserProfile as LibUserProfile, SecurityBindings, securityId} from '@loopback/security';
import {CognitoIdentityServiceProvider} from 'aws-sdk';
import axios from 'axios';
import {genSalt, hash} from 'bcryptjs';
import FormData from 'form-data';
import {DateTime} from 'luxon';
import moment from 'moment';
import multer from 'multer';
import {createTransport} from 'nodemailer';
import {filterDataByTierAndLocationByLevel, getRPTextFormat} from '../helper/filter-assignment-year-helper';
import {AssignDcfEntityUser, DealerChecklistSubmission, NewMetric, UserProfile} from '../models';
import {AssignDcfEntityUserRepository, AssignDcfUserRepository, ChangeManagementRepository, ConsolidateFormCollectionRepository, DpReportNewRepository, DpReportRepository, FormCollectionRepository, NewCategoryRepository, NewDataPointRepository, NewMetricRepository, NewTopicRepository, ReportNameTwoRepository, SupplierActionRepository, SupplierAssessmentAssignmentRepository, UserProfileRepository, UserRoleAuthorizationRepository, VendorCodeRepository} from '../repositories';
import {Helper} from '../services';
import {SqsService} from '../services/sqs-service.service';
import {ClientEfCategoryMappingController} from './client-ef-category-mapping.controller';
import {EmployeeDataController} from './employee-data.controller';
import {SapResponseController} from './sap-response.controller';
import {UserRoleAuthorizationController} from './user-role-authorization.controller';
import {UserController} from './user.controller';
const storage = multer.memoryStorage();
const upload = multer({storage});
const cognito = new CognitoIdentityServiceProvider({
  region: 'ap-southeast-1',
  accessKeyId: '********************',
  secretAccessKey: 'FTor4l60mdYT4W4OciDVcKeOQlOC6bLhqxGmtaDP'
})
const cognito2 = new CognitoIdentityServiceProvider({
  region: 'ap-south-1',
  accessKeyId: '********************',
  secretAccessKey: 'VJx9OgTegQ7Jgvo2+Grg8Lm7ZLZcLitMj0PDySaX'
})
const cognitoTVSExternal = new CognitoIdentityServiceProvider({

  region: process.env.REACT_APP_TVS_AWS_REGION,
  accessKeyId: '********************',
  secretAccessKey: 'ufrZ6kWejEcRmtmbu58kQgU1Kx93P6r91qJdt7C/'
})
const generator = require('generate-password');
const MailService = createTransport({
  port: 465,               // true for 465, false for other ports
  host: "smtp.gmail.com",
  auth: {
    user: '<EMAIL>',
    pass: 'dgwbhnasqxxgnxmb',
  },
  secure: true,
})
interface Details {
  [key: string]: string; // This allows any string as a property name with a string value
  grant_type: string;
  client_id: string;
  client_secret: string;
  username: string;
  password: string;
}


export class NewUserProfile extends UserProfile {
  @property({
    type: 'string',

  })
  username: string;

  @property({
    type: 'string',

  })
  email: string;
}

const userProfileSchema: SchemaObject = {
  type: 'object',

  properties: {
    email: {
      type: 'string',
      format: 'email',
    },
    company: {
      type: 'string',
    },
    active: {
      type: 'number',
    },
    username: {
      type: 'string',
    },
  },
};
type GroupDealerHeaderMail = {
  headMailIds: string[];
  [key: string]: any;
};

type FuelInfo = {
  NCV: number;
  Density: number;
  Formula: string;

};
export class UserProfileController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
    @repository(SupplierAssessmentAssignmentRepository) protected supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(SupplierActionRepository)
    public supplierActionRepository: SupplierActionRepository,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewCategoryRepository) public newCategoryRepository: NewCategoryRepository,
    @repository(UserCredentialsRepository) protected userCredentialsRepository: UserCredentialsRepository,
    @repository(UserRoleAuthorizationRepository) protected userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('controllers.ClientEfCategoryMappingController')
    public clientEfCategoryMappingController: ClientEfCategoryMappingController,
    @inject('services.HelperProvider')
    public helper: Helper,
    @inject('controllers.UserController')
    public userController: UserController,
    @inject('controllers.UserRoleAuthorizationController')
    public userRoleAuthorizationController: UserRoleAuthorizationController,
    @inject('controllers.SapResponseController')
    public sapResponseController: SapResponseController,
    @repository(AssignDcfUserRepository)
    public assignDcfUserRepository: AssignDcfUserRepository,
    @repository(AssignDcfEntityUserRepository)
    public assignDcfEntityUserRepository: AssignDcfEntityUserRepository,
    @repository(NewDataPointRepository)
    public newDataPointRepository: NewDataPointRepository,
    @inject('controllers.EmployeeDataController')
    public employeeDataController: EmployeeDataController,
    @repository(ReportNameTwoRepository)
    public reportNameTwoRepository: ReportNameTwoRepository,
    @repository(DpReportRepository)
    public dpReportRepository: DpReportRepository,
    @repository(DpReportNewRepository)
    public dpReportNewRepository: DpReportNewRepository,
    @repository(ChangeManagementRepository)
    public changeManagementRepository: ChangeManagementRepository,
    @repository(ConsolidateFormCollectionRepository)
    public consolidateFormCollectionRepository: ConsolidateFormCollectionRepository,
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(NewTopicRepository)
    public newTopicRepository: NewTopicRepository,

  ) { }


  @authenticate('jwt')
  @get('/get-me-new')
  async whoAmINew(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
  ): Promise<any> {

    const userId = currentUserProfile[securityId];
    const userDetail = await this.userRepository.findById(userId);
    const userProfileDetail = await this.userProfileRepository.findOne({
      where: {userId: userId}, include: [
        {
          relation: 'vendorCodes'
        },
      ], limit: 1
    });

    if (userProfileDetail && userProfileDetail.role === 'clientuser') {
      const clientDetails = await this.userProfileRepository.findOne({where: {id: userProfileDetail.clientId}, limit: 1});

      if (clientDetails) {
        const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: clientDetails.id, user_id: userProfileDetail.id}})
        return {...userProfileDetail, email: userDetail.email, admin: clientDetails, roles_data: role_data}
      } else {
        throw new Error('Admin Not Configured Properly')
      }

    } if (userProfileDetail && userProfileDetail.role === 'clientdealer') {
      const clientDetails = await this.userProfileRepository.findOne({where: {id: userProfileDetail.clientId}, limit: 1});

      if (clientDetails) {
        const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: clientDetails.id, user_id: userProfileDetail.id}})
        return {...userProfileDetail, email: userDetail.email, admin: clientDetails, roles_data: role_data}
      } else {
        throw new Error('Admin Not Configured Properly')
      }

    } else if (userProfileDetail && (userProfileDetail.role === 'clientadmin' || userProfileDetail.role === 'eisqradmin' || userProfileDetail.role === 'consultantadmin')) {
      const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: userProfileDetail.id, user_id: userProfileDetail.id}})

      return {...userProfileDetail, email: userDetail.email, admin: userProfileDetail, roles_data: role_data}
    } else if (userProfileDetail && userProfileDetail.role === 'clientsupplier') {
      const clientDetails = await this.userProfileRepository.findOne({
        where: {id: userProfileDetail.clientId}, limit: 1
      });
      ;
      if (clientDetails) {
        const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: clientDetails.id, user_id: userProfileDetail.id}})
        return {...userProfileDetail, email: userDetail.email, admin: clientDetails, roles_data: role_data}
      } else {
        throw new Error('Admin Not Configured Properly')
      }

    } else {
      throw new Error('User Not Found')
    }

  }
  @authenticate('jwt')
  @get('/get-me')
  async whoAmI(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
  ): Promise<any> {

    const userId = currentUserProfile[securityId];
    const userDetail = await this.userRepository.findById(userId);
    const userProfileDetail = await this.userProfileRepository.findOne({where: {userId: userId}, limit: 1});
    if (userProfileDetail && userProfileDetail.role === 'clientuser') {
      const clientDetails = await this.userProfileRepository.findOne({where: {id: userProfileDetail.clientId}, limit: 1});

      if (clientDetails) {
        return {...userProfileDetail, email: userDetail.email, admin: clientDetails}
      } else {
        return {...userProfileDetail, email: userDetail.email}
      }
    } else if (userProfileDetail && (userProfileDetail.role === 'clientadmin' || userProfileDetail.role === 'eisqradmin' || userProfileDetail.role === 'consultantadmin')) {


      return {...userProfileDetail, email: userDetail.email, admin: userProfileDetail}
    }
    else {
      throw new Error('User Not Found')
    }

  }

  @authenticate('cognito-jwt')
  @get('/user-get-me')
  async userwhoAmI(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
  ): Promise<any> {

    const userId = currentUserProfile.email;
    if (!userId) {
      throw new Error('Unauthorized')
    }

    const userDetail = await this.userRepository.findOne({where: {email: userId}});




    if (userDetail) {
      const userProfileDetail = await this.userProfileRepository.findOne({where: {userId: userDetail.id}, limit: 1});


      return {...userProfileDetail, email: userDetail.email}
    } else {
      throw new Error('Unauthorized')
    }



  }
  @authenticate('cognito-jwt')
  @get('/user-get-me-new')
  async usernewwhoAmI(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
  ): Promise<any> {

    const userId = currentUserProfile.email;
    if (!userId) {
      throw new Error('Unauthorized')
    }
    const userDetail = await this.userRepository.findOne({where: {email: userId}});
    if (userDetail) {
      const userProfileDetail = await this.userProfileRepository.findOne({where: {userId: userDetail.id}, limit: 1});

      if (userProfileDetail && userProfileDetail.role === 'clientuser') {
        const clientDetails = await this.userProfileRepository.findOne({where: {id: userProfileDetail.clientId}, limit: 1});

        if (clientDetails) {
          const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: clientDetails.id, user_id: userProfileDetail.id}})
          return {...userProfileDetail, email: userDetail.email, admin: clientDetails, roles_data: role_data}
        } else {
          throw new Error('Admin Not Configured Properly')
        }

      } else if (userProfileDetail && (userProfileDetail.role === 'clientadmin' || userProfileDetail.role === 'eisqradmin' || userProfileDetail.role === 'consultantadmin')) {
        const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: userProfileDetail.id, user_id: userProfileDetail.id}})

        return {...userProfileDetail, email: userDetail.email, admin: userProfileDetail, roles_data: role_data}
      }
      else {
        throw new Error('User Not Found')
      }
    } else {
      throw new Error('Unauthorized')
    }

  }

  @authenticate('cognito-tvs')
  @get('/get-me-tvs')
  async whoAmITVS(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
  ): Promise<any> {
    const userId = currentUserProfile.email;
    if (!userId) {

      throw new Error('Unauthorized')
    }
    const userDetail = await this.userRepository.findOne({where: {email: userId}});
    if (userDetail) {
      const userProfileDetail = await this.userProfileRepository.findOne({where: {userId: userDetail.id}, limit: 1});

      if (userProfileDetail && (userProfileDetail.role === 'clientuser' || userProfileDetail.role === 'clientextuser')) {
        const clientDetails = await this.userProfileRepository.findOne({where: {id: userProfileDetail.clientId}, limit: 1});

        if (clientDetails) {
          const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: clientDetails.id, user_id: userProfileDetail.id}})
          return {...userProfileDetail, email: userDetail.email, admin: clientDetails, roles_data: role_data}
        } else {
          throw new Error('Admin Not Configured Properly')
        }

      } if (userProfileDetail && userProfileDetail.role === 'clientdealer') {
        const clientDetails = await this.userProfileRepository.findOne({where: {id: userProfileDetail.clientId}, limit: 1});

        if (clientDetails) {
          const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: clientDetails.id, user_id: userProfileDetail.id}})
          return {...userProfileDetail, email: userDetail.email, admin: clientDetails, roles_data: role_data}
        } else {
          throw new Error('Admin Not Configured Properly')
        }

      } else if (userProfileDetail && (userProfileDetail.role === 'clientadmin')) {
        const role_data = await this.userRoleAuthorizationRepository.find({where: {userProfileId: userProfileDetail.id, user_id: userProfileDetail.id}})

        return {...userProfileDetail, email: userDetail.email, admin: userProfileDetail, roles_data: role_data}
      }

      else {
        throw new Error('User Not Found')
      }
    } else {
      throw new Error('Unauthorized')
    }

  }
  @post('/user-profiles')
  @response(200, {
    description: 'UserProfile model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserProfile)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserProfile, {
            title: 'NewUserProfile',
            exclude: ['id'],
          }),
        },
      },
    })
    userProfile: Omit<UserProfile, 'id'>,
  ): Promise<UserProfile> {
    return this.userProfileRepository.create(userProfile);
  }

  private fuelProperties: Record<string, FuelInfo> = {
    'S1-T21-G1-GS1-I1-1-4-1459': {NCV: 0.046, Density: 0, Formula: 'consumption * NCV'},
    'S1-T21-G1-GS1-I1-1-6-22': {NCV: 45.627, Density: 0, Formula: 'consumption * NCV'},
    'S1-T21-G1-GS1-I1-1-8-30': {NCV: 46.4, Density: 0, Formula: 'consumption * NCV'},
    'S1-T21-G1-GS1-I1-4-13-51': {NCV: 43.028, Density: 0.83, Formula: '((consumption * Density)/1000) * NCV'},
    'S1-T21-G1-GS1-I1-4-14-55': {NCV: 40.752, Density: 0.983, Formula: '((consumption * Density)/1000) * NCV'},
    'S1-T21-G1-GS1-I1-4-19-75': {NCV: 44.599, Density: 0.74626, Formula: '((consumption * Density)/1000) * NCV'}
  };

  // @post('/new-user-profiles')
  // @response(200, {
  //   description: 'UserProfile model instance',
  //   content: {'application/json': {schema: getModelSchemaRef(UserProfile)}},
  // })
  // async createNew(
  //   @requestBody()
  //   userProfile: any,
  // ): Promise<any> {

  //   const email = userProfile.email;
  //   const role = userProfile.role;
  //   const information = userProfile.information;
  //   const cid = userProfile.clientId;
  //   const genPassword = generator.generate({
  //     length: 10,
  //     numbers: true
  //   });

  //   const username = userProfile.information ? userProfile.information.username ? userProfile.information.username : '' : ''
  //   const password = await hash(genPassword, await genSalt());

  //   const savedUser = await this.userRepository.create(
  //     {email: email, username: username}

  //   );

  //   await this.userRepository.userCredentials(savedUser.id).create({password});
  //   await this.userProfileRepository.create({active: true, role: role, information: information, userId: savedUser.id, clientId: cid});
  //   let mailData = {
  //     from: '<EMAIL>',  // sender address
  //     to: email,   // list of receivers
  //     subject: 'Eisqr Account Created',
  //     text: '',
  //     html: `<b>Hi! </b>
  //              <br> Your account has been created as ${role}. Please login using the password: ${genPassword}  <br/>`,
  //   };

  //   MailService.sendMail(mailData, (err, info) => {
  //     if (err)
  //       return false
  //     else
  //       return true
  //   });
  //   return savedUser;
  // }

  @post('/new-user-profiles')
  @response(200, {
    description: 'UserProfile model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserProfile)}},
  })
  async createNew(
    @requestBody()
    userProfile: any,
  ): Promise<any> {


    const email = userProfile.email;
    const role = userProfile.role;
    const information = userProfile.information;
    const cid = userProfile.clientId;
    let admin_portal = '', user_portal = '', supplier_portal = '', ssoLogin = false
    const genPassword = this.generateRandomString()

    if (!genPassword) {
      throw new HttpErrors.Conflict('Password is Empty');
    }
    const username = userProfile.information ? userProfile.information.username ? userProfile.information.username : '' : ''
    const password = await hash(genPassword, await genSalt());
    // if (userProfile.role === 'clientsupplier' && userProfile.supplierCode) {
    //   const savedUserList = await this.userProfileRepository.find({where: {supplierCode: userProfile.supplierCode}})
    //   if (savedUserList.length > 0) {
    //     throw new HttpErrors.Conflict('Supplier Code Already Exists')
    //   }
    // }

    const savedUser = await this.userRepository.create(
      {email: email, username: username}


    );


    await this.userRepository.userCredentials(savedUser.id).create({password});
    let newObj = {}
    if (userProfile && role === 'clientadmin') {
      newObj = {coginfo: userProfile.coginfo, userPortalUrl: userProfile.userPortalUrl, adminPortalUrl: userProfile.adminPortalUrl, supplierPortalUrl: userProfile.supplierPortalUrl, ssoLogin: userProfile.ssoLogin, active: true, role: role, information: information, userId: savedUser.id, access: userProfile.access, fyStartMonth: userProfile.fyStartMonth, tierLabel: userProfile.tierLabel}
    } if (userProfile && (role === 'clientuser' || role === 'clientsupplier')) {
      newObj = {active: true, role: role, supplierCode: userProfile.supplierCode, information: information, userId: savedUser.id, clientId: cid}

    } else {
      newObj = {active: true, role: role, dealerCode: userProfile.dealerCode, information: information, userId: savedUser.id, clientId: cid}
    }
    const createdUser = await this.userProfileRepository.create(newObj);
    if (savedUser && createdUser) {
      let name = 'User'
      let role_name = 'User'
      let company_name = ''
      let contact_mail = null
      if (role === 'clientadmin') {
        name = information.companyname
        company_name = information.companyname
        role_name = 'Enterprise Administrator'
        if (createdUser.adminPortalUrl) {
          admin_portal = createdUser.adminPortalUrl
        }
        if (createdUser.userPortalUrl) {
          user_portal = createdUser.userPortalUrl
        }
        if (createdUser.ssoLogin) {
          ssoLogin = true
        }

        // this.userProfileRepository.updateById(createdUser.id, {clientId: createdUser.id})
      } else if (role === 'clientuser') {
        const data = await this.userProfileRepository.find({where: {id: cid}})
        // this.userRoleAuthorizationRepository.create({user_id: createdUser.id, userProfileId: cid, created_by: cid, created_on: DateTime.utc().toString(), tier1_id: 0, tier2_id: null, tier3_id: null})

        if (data.length) {
          company_name = data[0].information.companyname

          const data2 = await this.userRepository.findById(data[0].userId)
          contact_mail = data2.email
          if (data[0].adminPortalUrl) {
            admin_portal = data[0].adminPortalUrl
          }
          if (data[0].userPortalUrl) {
            user_portal = data[0].userPortalUrl
          }
          if (data[0].ssoLogin) {
            ssoLogin = true
          }
        }
        name = information.empname
        role_name = ' User '
      } else if (role === 'clientsupplier') {
        const data = await this.userProfileRepository.find({where: {id: cid}})
        if (data.length) {
          company_name = data[0].information.companyname
          const data2 = await this.userRepository.findById(data[0].userId)
          contact_mail = data2.email

          if (data[0].supplierPortalUrl) {
            supplier_portal = data[0].supplierPortalUrl
          }

        }
        name = information.supplierName
        role_name = ' Supplier User '

      } else if (role === 'clientdealer') {
        const data = await this.userProfileRepository.find({where: {id: cid}})
        if (data.length) {
          company_name = data[0].information.companyname
          const data2 = await this.userRepository.findById(data[0].userId)
          contact_mail = data2.email
          if (data[0].id === 289) {
            ssoLogin = true
          }

        }
        name = information.dealerName
        role_name = ' Dealer '

      }
      else if (role === 'consultantadmin') {
        name = information.companyname
        company_name = information.companyname
        role_name = ' Consultant Admin '
        // this.userProfileRepository.updateById(createdUser.id, {clientId: createdUser.id})
      } else if (role === 'consultantclient') {
        const tempPassword = 'Test@123';

        const params = {
          UserPoolId: 'ap-southeast-1_YCkc5yqn9',
          Username: email,
          TemporaryPassword: genPassword,
          MessageAction: 'SUPPRESS',
          UserAttributes: [
            {
              Name: 'email',
              Value: userProfile.client.information.email
            },
            {
              Name: 'email_verified',
              Value: 'true'
            }
          ]
        };

        // Create the user with a temporary password
        const awsNewUser = await cognito.adminCreateUser(params).promise();

        // Immediately set the permanent password
        const setPasswordParams = {
          UserPoolId: 'ap-southeast-1_YCkc5yqn9',
          Username: email,
          Password: genPassword,  // Use the same password or a new one if needed
          Permanent: true
        };

        await cognito.adminSetUserPassword(setPasswordParams).promise();


      }

      const body = `<p>Dear ${name},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform. As a ${company_name} , you have been assigned the role of  <strong>${role_name} </strong> </p>${admin_portal ? `<p>NAVIGOS ESG Portal URL :</p><p>Admin Portal: <a href=${admin_portal}>${admin_portal}</a> </p><p>User Portal: <a href=${user_portal}>${user_portal}</a>  </p> ${supplier_portal ? `<p>Supplier Portal: <a href=${supplier_portal}>${supplier_portal}</a>  </p>` : ''}  ` : ''}  <p>Please find below your login credentials: </p><p>Username/Email: <strong>${email}</strong></p><p> Password: <strong>${(cid === 289 && role === 'clientuser') ? "Login via SSO" : genPassword} </strong></p><p>${ssoLogin ? "Since you will be logging in via SSO, no password is required. Simply use your SSO credentials to access your account" : "Kindly save the provided credentials to access your account on Navigos until further notice. Upon initial login, we recommend changing your password for security purposes."}</p><p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
      const mailData = {
        from: '<EMAIL>',  // sender address
        to: email,   // list of receivers
        subject: 'NAVIGOS ACCOUNT CREATION',
        text: '',
        html: body
      };

      // html: `<b>Hi! </b>
      // <br> Your account has been created as ${role}. Please login using the password: ${genPassword}  <br/>`,

      // MailService.sendMail(mailData, (err, info) => {
      //   if (err) {
      //     ;
      //     return false
      //   }
      //   else {
      //     ;
      //     return true
      //   }
      // });

      this.sqsService.sendEmail(email, 'NAVIGOS ACCOUNT CREATION', body, []).then((info) => {

        return true
      }).catch((err) => {

        return false
      })



      return savedUser
    } else {
      throw new Error('Something went wrong')
    }

  }

  @post('/new-tvs-external-users')
  @response(200, {
    description: 'UserProfile model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserProfile)}},
  })
  async createNewTVSExternalUser(
    @requestBody()
    newUserRequest: any,
  ): Promise<any> {
    const {clientId, information, email} = newUserRequest
    try {
      newUserRequest.resetKey = '';
      const tempPassword = this.generateRandomString();
      newUserRequest.password = tempPassword;

      const params = {
        UserPoolId: `${process.env.REACT_APP_TVS_AWS_EXTERNAL_USER_POOL_ID}`,
        Username: email,
        TemporaryPassword: tempPassword,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {
            Name: 'email',
            Value: email
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ]
      };

      const awsNewUser = await cognitoTVSExternal.adminCreateUser(params).promise();

      if (awsNewUser.User?.Username) {
        let company_name = '', admin_portal = 'https://tvsmotor.eisqr.com'
        const data = await this.userProfileRepository.find({where: {id: clientId}})

        if (data.length) {

          company_name = data[0].information.companyname
          if (data[0].userPortalUrl) {
            admin_portal = data[0]?.userPortalUrl || ''
          }

        }
        const savedUser = await this.userRepository.create(
          {email: email, username: awsNewUser.User?.Username}
        );


        const password = await hash(tempPassword, await genSalt());
        await this.userRepository.userCredentials(savedUser.id).create({password: password});
        const newObj = {active: true, role: 'clientextuser', userId: savedUser.id, clientId, information, cognitoRefUserName: awsNewUser.User?.Username}

        const createdUser = await this.userProfileRepository.create(newObj);
        if (createdUser) {
          const body = `<p>Dear ${information?.empname || 'User'},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a External User. As a ${information?.orgname} , you have been assigned the role of  <strong>External User</strong> </p><p>Portal url: <a href=${admin_portal}>${admin_portal}</a>  </p>  <p>Please find below your login credentials: </p><p>Username/Email: <strong>${email}</strong></p><p> Temporary Password: <strong>${tempPassword} </strong></p><p>  Upon initial login, we recommend to changing your password for security purposes.</p><p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

          const info = await this.sqsService.sendEmail(email, 'NAVIGOS ACCOUNT CREATION - TVSM', body, []).then((info) => {

            return info
          }).catch((err) => {

            return {status: false, message: err.errmsg}
          })
          return {status: true, message: 'External User added successfully', data: createdUser}


          // const data = await this.vendorCodeRepository.create({...information, userProfileId: createdUser.id, code: supplierCode, clientId: clientId})
          // return {status: true, message: 'Added new supplier with given vendor code', data: {...createdUser, information: data}}
          // const body = `<p>Dear ${name},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform. As a ${company_name} , you have been assigned the role of  <strong>Supplier</strong> </p><p>Supplier Portal: <a href=${supplier_portal}>${supplier_portal}</a>  </p>  <p>Please find below your login credentials: </p><p>Username/Email: <strong>${email}</strong></p><p> Temporary Password: <strong>${tempPassword} </strong></p><p>  Upon initial login, we recommend to changing your password for security purposes.</p><p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

        }

      } else {
        return {status: false, message: 'User Not Added into Cognito, if issue <NAME_EMAIL>'}

      }
    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        return {status: false, message: 'User Already Added into Cognito,if issue <NAME_EMAIL>'}
      } else {

        return {status: false, message: 'Something went wrong,if issue <NAME_EMAIL>'}

      }
    }
  }

  @get('/user-profiles/count')
  @response(200, {
    description: 'UserProfile model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(UserProfile) where?: Where<UserProfile>,
  ): Promise<Count> {
    return this.userProfileRepository.count(where);
  }

  @get('/user-profiles')
  @response(200, {
    description: 'Array of UserProfile model instances',
    content: {
      'application/json': {
        schema: userProfileSchema,
      },
    },
  })
  async find(

  ): Promise<any> {
    const users = await this.userProfileRepository.find();

    const returnUsers = await Promise.all(users.map(async (user) => {
      const userData = await this.userRepository.findById(user.userId);

      const newData = {...user, username: userData.username, email: userData.email};
      return newData;
    }));


    return returnUsers;
  }

  @get('/user-profiles/{id}/supplier-list-custom')
  @response(200, {
    description: 'Array of UserProfile model instances',
    content: {
      'application/json': {
        schema: userProfileSchema,
      },
    },
  })
  async getSupplier(
    @param.path.number('id') id: number,
  ): Promise<any> {
    const users = await this.userProfileRepository.find({where: {clientId: id, role: 'clientsupplier'}, include: [{relation: 'vendorCodes'}]});
    const returnUsers = await Promise.all(users.map(async (user) => {
      const userData = await this.userRepository.findById(user.userId);

      const newData = {...user, username: userData.username, email: userData.email};
      return newData;
    }));
    return this.supplierTransformData(returnUsers);
  }
  @get('/user-profiles/{id}/dealer-list-custom')
  @response(200, {
    description: 'Array of UserProfile model instances',
    content: {
      'application/json': {
        schema: userProfileSchema,
      },
    },
  })
  async getDealer(
    @param.path.number('id') id: number,
  ): Promise<any> {
    const users = await this.userProfileRepository.find({where: {clientId: id, role: 'clientdealer'}, include: ['vendorCodes']});
    const returnUsers = await Promise.all(users.map(async (user) => {
      const userData = await this.userRepository.findById(user.userId);

      const newData = {...user, username: userData.username, email: userData.email};
      return newData;
    }));
    return this.dealerTransformData(returnUsers);
  }




  @get('/user-profiles/{id}/dcf-user-assignment-approver')
  @response(200, {
    description: 'Array of UserProfile model instances',
    content: {
      'application/json': {
        schema: userProfileSchema,
      },
    },
  })
  async getAssignedIndicatorList(
    @param.path.number('id') id: number, @param.filter(AssignDcfEntityUser) filter?: Filter<AssignDcfEntityUser>,
  ): Promise<any> {
    const assingedIndicator = await this.userProfileRepository.assignDcfClients(id).find()
    try {
      if (assingedIndicator.length === 1) {
        const indicator_list: NewMetric[] = []
        const assignedIndicator = assingedIndicator[0]
        const esgCategory = await this.newCategoryRepository.find({
          include: [
            {
              relation: "newTopics",
              scope: {
                include: [{
                  relation: "newMetrics", scope: {
                    include: ["newDataPoints"],
                  }
                }],
              },
            },
          ],
        });
        const shapedCategory = esgCategory.map(item => {
          if (item.newTopics) {
            item.newTopics = item.newTopics.filter(topics =>
              topics.newMetrics && topics.newMetrics.length > 0
            );
          }
          return item;
        }).filter(item => item.newTopics && item.newTopics.length > 0)
        shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
          if (assignedIndicator.topic_ids && top.id && assignedIndicator.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === id)) {
            top.newMetrics.forEach((met) => {

              if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0) && met.id && assignedIndicator.metric_ids && assignedIndicator.metric_ids.includes(met.id) && !indicator_list.map(i => i.id).includes(met.id) && (met.tag === null || parseFloat(met.tag) === id)) {
                indicator_list.push(met)

              }
            })
          }
        })
        const filteredLocations = await this.userProfileRepository.locationOnes(id).find({
          include: [
            {
              relation: "locationTwos",
              scope: {
                include: [{relation: "locationThrees"}],
              },
            },
          ],
        });

        const shapedSite = filteredLocations.map(item => {
          if (item.locationTwos) {
            item.locationTwos = item.locationTwos.filter(locationTwo =>
              locationTwo.locationThrees && locationTwo.locationThrees.length > 0
            );
          }
          return item;
        }).filter(item => item.locationTwos && item.locationTwos.length > 0);
        const entityAssignment = await this.userProfileRepository.assignDcfEntities(id).find()

        const entityUserAssingment = (await this.userProfileRepository.assignDcfEntityUsers(id).find({...filter, include: ['dcf']}))
        const locations = await this.userProfileRepository.locationOnes(id).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

        const locations0 = [0]
        const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
        const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
        const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
        const locationMap: any = {
          0: locations0,
          1: locations1,
          2: locations2,
          3: locations3
        };

        const filteredAssignments = entityUserAssingment.filter((assignment) => {
          return entityAssignment.some((ent: any) => {
            const tierKey = `tier${assignment.level}_ids`;
            const validLocations = locationMap[assignment?.level || 0] || [];

            // Filter out invalid IDs from ent[tierKey]
            const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
              validLocations.includes(Number(id))
            );



            // Check if assignment.locationId is present in the filtered IDs
            const isLocationMatch = filteredTierIds.includes(Number(assignment.locationId));

            const isBasicMatch =
              ent.dcfId === assignment.dcfId
            return isBasicMatch && isLocationMatch;
          });
        });




        const res = await this.filterDerivedAndStandaloneWithIds(indicator_list, shapedCategory.flatMap(x => x?.newTopics?.flatMap(y => y?.newMetrics || []) || []), id)
        const indicatorAssignment = await this.userProfileRepository.indicatorApproverAssignments(id).find()

        const result = this.mapApproverIds(indicatorAssignment.map(i => ({...i, dcfIds: res.find(x => x.id === i.indicatorId && x.standalone_ids.length === 1)?.dcfIds || []})), filteredAssignments.map(x => ({...x, approver_ids: []})), shapedSite)
        // Get users by roles using the dynamic method
        const roleAssignment = await this.userRoleAuthorizationController.getUsersByRolesDynamic(id, {roles: [1, 4, 3]});
        const [reporters, reviewers, approvers] = roleAssignment;

        for (const item of result) {
          item.reporter_ids = item.reporter_ids.filter((x: any) => reporters.includes(x) || id === x);
          item.reviewer_ids = item.reviewer_ids.filter((x: any) => reviewers.includes(x) || id === x);
          item.approver_ids = item.approver_ids.filter((x: any) => approvers.includes(x) || id === x);
        }
        return result


      }
    } catch (e) {
      console.log(e)
    }
  }

  flattenLocationData(locationData: any[]): Record<number, {
    id: number;
    level: number;
    parentId: number | null;
    allParentIds: number[];
  }> {
    const map: Record<number, {
      id: number;
      level: number;
      parentId: number | null;
      allParentIds: number[];
    }> = {};

    // Flatten Tier 1 (Country level)
    locationData.forEach((tier1: any) => {
      if (!tier1?.id) return;
      map[tier1.id] = {id: tier1.id, level: 1, parentId: null, allParentIds: []};

      // Flatten Tier 2 (State level)
      tier1.locationTwos?.forEach((tier2: any) => {
        if (!tier2?.id) return;
        map[tier2.id] = {id: tier2.id, level: 2, parentId: tier1.id, allParentIds: []};

        // Flatten Tier 3 (City level)
        tier2.locationThrees?.forEach((tier3: any) => {
          if (!tier3?.id) return;
          map[tier3.id] = {id: tier3.id, level: 3, parentId: tier2.id, allParentIds: []};
        });
      });
    });

    // Now build allParentIds chain safely
    for (const id in map) {
      const currentId = Number(id);
      const parents: number[] = [];
      let current = map[currentId];

      const visited = new Set<number>(); // prevent circular loops

      while (current && current.parentId !== null) {
        if (visited.has(current.parentId)) break; // prevent infinite loop
        visited.add(current.parentId);

        parents.push(current.parentId);
        current = map[current.parentId];
      }

      map[currentId].allParentIds = parents;
    }

    return map;
  }

  extractIndicatorLocationIds(loc: {
    tier1_id: number;
    tier2_id: number;
    tier3_id: number;
  }): number[] {
    return [loc.tier3_id, loc.tier2_id, loc.tier1_id].filter(id => id && id !== 0);
  }
  mapApproverIds(
    indicatorAssignments: any[],
    dcfAssignments: any[],
    locationData: any[]
  ): any[] {
    const locationMap = this.flattenLocationData(locationData);

    return dcfAssignments.map((dcf: any) => {


      const entity = locationMap[dcf.locationId];
      const dcfEntityPath = [dcf.locationId, ...(entity?.allParentIds || [])];

      const matchingIndicator = indicatorAssignments.find((indicator: any) =>
        indicator.dcfIds.includes(dcf.dcfId) &&
        indicator.locations.some((loc: any) => {
          const indicatorIds = this.extractIndicatorLocationIds(loc);
          return indicatorIds.some((id: number) => dcfEntityPath.includes(id));
        })
      );

      if (matchingIndicator) {
        dcf.approver_ids = matchingIndicator.responsibility || [];
      }

      return dcf;
    });
  }

  dealerTransformData(data: any) {

    return data.flatMap((item: any) => {
      if (!item.vendorCodes || !Array.isArray(item.vendorCodes)) {


        if (item.information) {
          const info = item.information
          info.userProfileId = item.id
          info.clientId = item.clientId
          delete info.blocked
          delete info.cid
          return [{
            ...info, email: item.email, supplierCode: '', dealerName: '',
            emailSentCount: item.emailSentCount,
            dealerSPOC: '',
            dealerCategory: null,
            dealerZone: null,
            dealerLocation: '',
            dealerCountry: '',
            dealerAO: '',
            service: {
              areaManagerName: '',
              areaManagerMailId: '',
              zonalPlannerName: '',
              zonalPlannerMailId: '',
              regionalManagerName: '',
              regionalManagerMailId: '',
            },
            sales: {
              areaManagerName: '',
              areaManagerMailId: '',
              zonalPlannerName: '',
              zonalPlannerMailId: '',
              regionalManagerName: '',
              regionalManagerMailId: '',
            },
            aps: {
              areaManagerName: '',
              areaManagerMailId: '',
              regionalManagerName: '',
              regionalManagerMailId: '',
              hoPlannerName: '',
              hoPlannerMailId: '',
            },
            ao: {
              areaCommercialManagerName: '',
              areaCommercialManagerMailId: '',
              regionalCommercialManagerName: '',
              regionalCommercialManagerMailId: '',
            }
          }];
        } else {
          return [{
            userProfileId: item.id, clientId: item.clientId, supplierCode: '', emailSentCount: item.emailSentCount,
            dealerName: '',
            email: '',
            dealerSPOC: '',
            dealerCategory: null,
            dealerZone: null,
            dealerLocation: '',
            dealerCountry: '',
            dealerAO: '',
            service: {
              areaManagerName: '',
              areaManagerMailId: '',
              zonalPlannerName: '',
              zonalPlannerMailId: '',
              regionalManagerName: '',
              regionalManagerMailId: '',
            },
            sales: {
              areaManagerName: '',
              areaManagerMailId: '',
              zonalPlannerName: '',
              zonalPlannerMailId: '',
              regionalManagerName: '',
              regionalManagerMailId: '',
            },
            aps: {
              areaManagerName: '',
              areaManagerMailId: '',
              regionalManagerName: '',
              regionalManagerMailId: '',
              hoPlannerName: '',
              hoPlannerMailId: '',
            },
            ao: {
              areaCommercialManagerName: '',
              areaCommercialManagerMailId: '',
              regionalCommercialManagerName: '',
              regionalCommercialManagerMailId: '',
            }
          }];
        }
        // Handle undefined or null vendorCodes

      }
      const {vendorCodes, ...rest} = item
      // Transform vendorCodes into individual objects
      return item.vendorCodes.map((vendor: any) => ({
        ...vendor, email: item.email, emailSentCount: item.emailSentCount
      }));
    });
  }
  supplierTransformData(data: any) {

    return data.filter((x: any) => Array.isArray(x?.vendorCodes)).flatMap((item: any) => {
      if (!item.vendorCodes || !Array.isArray(item.vendorCodes)) {


        if (item.information) {
          const info = item.information
          info.userProfileId = item.id
          info.clientId = item.clientId
          delete info.blocked
          delete info.cid
          return [{...info, supplierEmail: item.email, emailSentCount: item.emailSentCount}];
        } else {
          return [{userProfileId: item.id, emailSentCount: item.emailSentCount, clientId: item.clientId, supplierSpentOn: 0, supplierName: '', supplierSPOC: '', supplierCode: '', supplierLocation: '', plantLocation: null, supplierContact: null, supplierEmail2: '', supplierContact2: null, supplierEmail3: '', supplierContact3: null, supplierCategory: null, supplierEmail: item.email}];
        }
        // Handle undefined or null vendorCodes

      }
      const {vendorCodes, ...rest} = item
      // Transform vendorCodes into individual objects
      return item.vendorCodes.map((vendor: any) => ({
        ...vendor, supplierEmail: item.email, emailSentCount: item.emailSentCount
      }));
    });
  }


  // @patch('/user-profiles')
  // @response(200, {
  //   description: 'UserProfile PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(UserProfile, {partial: true}),
  //       },
  //     },
  //   })
  //   userProfile: UserProfile,
  //   @param.where(UserProfile) where?: Where<UserProfile>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.updateAll(userProfile, where);
  // }
  @get('/user-profiles-filtered')
  @response(200, {
    description: 'Array of UserProfile model instances',
    content: {
      'application/json': {
        schema: userProfileSchema,
      },
    },
  })
  async filteredUP(
    @param.filter(UserProfile) filter?: Filter<UserProfile>
  ): Promise<any> {
    const users = await this.userProfileRepository.find(filter);

    const returnUsers = await Promise.all(users.map(async (user) => {
      const userData = await this.userRepository.findById(user.userId);

      const newData = {...user, username: userData.username, email: userData.email};
      return newData;
    }));


    return returnUsers;
  }
  @get('/user-profiles/{id}')
  @response(200, {
    description: 'UserProfile model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserProfile, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(UserProfile, {exclude: 'where'}) filter?: FilterExcludingWhere<UserProfile>
  ): Promise<UserProfile> {
    return this.userProfileRepository.findById(id, filter);
  }

  @patch('/user-profiles/{id}')
  @response(204, {
    description: 'UserProfile PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserProfile, {partial: true}),
        },
      },
    })
    userProfile: UserProfile,
  ): Promise<void> {

    try {


      await this.userProfileRepository.updateById(id, userProfile);
    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }

  @patch('/user-profiles-email/{id}')
  @response(204, {
    description: 'UserProfile PATCH success',
  })
  async updateEmailById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserProfile, {partial: true}),
        },
      },
    })
    userProfile: UserProfile,
  ): Promise<void> {
    await this.userProfileRepository.updateById(id, userProfile);
  }

  @put('/user-profiles/{id}')
  @response(204, {
    description: 'UserProfile PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() userProfile: UserProfile,
  ): Promise<void> {
    await this.userProfileRepository.replaceById(id, userProfile);
  }

  @del('/user-profiles/{id}')
  @response(204, {
    description: 'UserProfile DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.userProfileRepository.deleteById(id);
  }

  @get('/get-reporters-mailids', {
    responses: {
      '200': {
        description: 'Mail Ids',
      },
    },
  })
  async getReporterIds(): Promise<any> {
    const data = await this.userProfileRepository.find({where: {or: [{id: 17}, {id: 28}]}});
    const mailArray: any[] = [];

    try {
      for (const i of data) {
        if (
          i.role === 'clientadmin' &&
          i.information !== undefined &&
          i.information !== null
        ) {
          const {user_remainder_mail_date, user_escalation_mail_date} = i.information;
          if (
            (user_remainder_mail_date !== undefined &&
              parseInt(user_remainder_mail_date) > 0 &&
              parseInt(user_remainder_mail_date) <= 30) || (user_escalation_mail_date !== undefined &&
                parseInt(user_escalation_mail_date) > 0 &&
                parseInt(user_escalation_mail_date) <= 30)
          ) {
            const currentDate = DateTime.utc().get('day');

            if ((currentDate === parseInt(user_remainder_mail_date)) || (currentDate === parseInt(user_escalation_mail_date))) {
              const dcfAssignment = await this.assignDcfUserRepository.find({
                where: {userProfileId: i.id},
              });
              const userIds: number[] = [];
              for (const userAss of dcfAssignment) {
                if (userAss.user_id !== undefined && !userIds.includes(userAss.user_id)) {
                  userIds.push(userAss.user_id);
                }
              }
              const idList = await this.userProfileRepository.find({where: {id: {inq: userIds}}});
              const returnUsers = await Promise.all(idList.map(async (user) => {
                const userData = await this.userRepository.findById(user.userId);

                return {
                  email: userData.email, name: user.information.empname, subject: 'Submission Overdue Reminder  - NAVIGOS', body: `<div>
                <p>Hello, ${user.information.empname}</p>
                <p >Following is a list of submissions that are needed from your end for completeness of the sustainability data reporting. Please complete these at the earliest so that the can be further processed and included in the performance dashboards and reports as required.</p>
                <p>To know you overdue submissions  <a href="https://navigos.eisqr.com/#/submitter_overdue">click here</a></p>
                <p >This is an automated message. Please do not respond to this mail</p> </div>` };
              }))
              mailArray.push({id: i.id, email: returnUsers});
            }
          }
        }
      }
      return mailArray;
    } catch (err) {
      console.error(err);
      return [];
    }
  }
  @get('/send-reminder-reporters', {
    responses: {
      '200': {
        description: 'Mail Ids',
      },
    },
  })
  async sendDCFReporterSummary(): Promise<any> {
    const data = await this.filteredUP({where: {role: 'clientadmin', id: {inq: [28, 51]}}});
    const mailArray: any[] = [];

    try {
      for (const i of data) {
        if (
          i.role === 'clientadmin' &&
          i.information != null
        ) {

          const userPortal = i?.userPortalUrl || null
          const {user_remainder_mail_date, user_escalation_mail_date} = i.information;
          if (
            (user_remainder_mail_date !== undefined &&
              parseInt(user_remainder_mail_date) > 0 &&
              parseInt(user_remainder_mail_date) <= 30) || (user_escalation_mail_date !== undefined &&
                parseInt(user_escalation_mail_date) > 0 &&
                parseInt(user_escalation_mail_date) <= 30)
          ) {
            let currentDate = DateTime.utc().get('day');
            const currentMonth = DateTime.utc().get('month');
            const lastDayOfMonth = DateTime.utc().endOf('month').day;

            if (currentMonth === 2 && currentDate === lastDayOfMonth && (parseInt(user_remainder_mail_date) >= lastDayOfMonth || parseInt(user_escalation_mail_date) >= lastDayOfMonth)) {
              if (parseInt(user_remainder_mail_date) >= lastDayOfMonth) {
                currentDate = user_remainder_mail_date
              } else if (parseInt(user_escalation_mail_date) >= lastDayOfMonth) {
                currentDate = user_escalation_mail_date
              }
            }
            const count = (currentDate === parseInt(user_escalation_mail_date)) ? 2 : 1
            if ((currentDate === parseInt(user_remainder_mail_date)) || (currentDate === parseInt(user_escalation_mail_date))) {
              const clientIndicatorAssignment = await this.userProfileRepository.assignDcfClients(i.id).find()
              const assignedData = clientIndicatorAssignment[clientIndicatorAssignment.length - 1]
              let assignedDcf: any = []
              const reportData: any[] = [];
              if (assignedData?.metric_ids?.length && assignedData?.topic_ids?.length) {
                const esgCategory = await this.newCategoryRepository.find({
                  include: [
                    {
                      relation: "newTopics",
                      scope: {
                        include: [{
                          relation: "newMetrics", scope: {
                            include: ["newDataPoints"],
                          }
                        }],
                      },
                    },
                  ],
                });
                const shapedCategory = esgCategory.map(item => {
                  if (item.newTopics) {
                    item.newTopics = item.newTopics.filter(topics =>
                      topics.newMetrics && topics.newMetrics.length > 0
                    );
                  }
                  return item;
                }).filter(item => item.newTopics && item.newTopics.length > 0)
                shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
                  if (assignedData.topic_ids && top.id && assignedData.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === i.id)) {
                    top.newMetrics.forEach((met) => {
                      if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0 && met.data1[0].source === 1) && met.id && assignedData.metric_ids && assignedData.metric_ids.includes(met.id) && (met.tag === null || parseFloat(met.tag) === i.id)) {
                        const overallDcfs = met?.newDataPoints?.flatMap((dp: any) => {
                          // Extract datasource and dcf_ids from each data point
                          const datasources = dp.data1?.map((d: any) => d.datasource).filter((ds: any) => ds != null) || [];
                          const dcfIds = dp.data1?.flatMap((d: any) => {
                            // Ensure dcf_ids is always an array
                            const dcfIdsValue = d.dcf_ids;
                            if (Array.isArray(dcfIdsValue)) {
                              return dcfIdsValue;
                            } else {
                              return []; // If not array (string, number, etc.), treat as empty array
                            }
                          }).filter((dcfId: any) => dcfId != null) || [];

                          // Combine both datasources and dcf_ids
                          return [...datasources, ...dcfIds];
                        })
                          .filter((dcfId: any) => dcfId) // Remove null/undefined values
                          .filter((v: any, i: any, a: any) => a.indexOf(v) === i); // Get unique values

                        if (overallDcfs) {
                          assignedDcf = Array.from(new Set([...assignedDcf, ...overallDcfs]));

                        }

                      }
                    })
                  }
                })

                const dcfEntityAssignment = await this.userProfileRepository.assignDcfEntities(i.id).find({where: {dcfId: {inq: assignedDcf}}})
                if (dcfEntityAssignment.length) {
                  const approverDCFdata = await this.getAssignedIndicatorList(i.id)
                  const locations = await this.userProfileRepository.locationOnes(i.id).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})
                  const locations0 = [0]
                  const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
                  const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locationMap: any = {
                    0: locations0,
                    1: locations1,
                    2: locations2,
                    3: locations3
                  };
                  const filteredAssignments = approverDCFdata.filter((assignment: any) => assignment.level === 3 ? ![278, 284].includes(assignment.locationId) : true).filter((assignment: any) => {
                    return dcfEntityAssignment.some((ent: any) => {
                      const tierKey = `tier${assignment.level}_ids`;
                      const validLocations = locationMap[assignment?.level || 0] || [];
                      const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
                        validLocations.includes(Number(id))
                      );

                      const isLocationMatch = ent.dcfId === assignment.dcfId && filteredTierIds.includes(Number(assignment.locationId));
                      if (assignment.level === 0) {
                        console.log(assignment.id, assignment.locationId, ent.id, isLocationMatch, ent[tierKey])
                      }
                      return isLocationMatch;
                    });
                  });

                  const quantitativeSubmisisons = await this.userProfileRepository.quantitativeSubmissions(i.id).find({where: {dcfId: {inq: assignedDcf}}})
                  const reporter_ids = filteredAssignments.flatMap((d: any) => d?.reporter_ids || []);
                  const reviewer_ids = filteredAssignments.flatMap((d: any) => d?.reviewer_ids || []);
                  const approver_ids = filteredAssignments.flatMap((d: any) => d?.approver_ids || []);
                  const userList = await this.filteredUP({where: {id: {inq: [...reporter_ids, ...reviewer_ids, ...approver_ids]}}})

                  for (const assignment of filteredAssignments) {
                    const expectedPeriods = this.calculateReportingPeriods(
                      assignment.start_date,
                      assignment.end_date,
                      assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency, i.id === 289 ? 'Apr-2024' : ''
                    );


                    const [reporters, reviewers, approvers, entity] = await Promise.all([
                      this.getUsersByIds(assignment.reporter_ids, userList),
                      this.getUsersByIds(assignment.reviewer_ids, userList),
                      this.getUsersByIds(assignment.approver_ids, userList),
                      this.getSortedEntity(assignment.level, assignment.locationId, locations)
                    ]);
                    const reporterNames = reporters.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const reviewerNames = reviewers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const approverNames = approvers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));

                    for (const periodGroup of expectedPeriods) {
                      const formattedPeriods = periodGroup.map(period => {
                        const [month, year] = period.split('-');
                        return `${month.padStart(2, '0')}-${year}`;
                      });

                      const submission = quantitativeSubmisisons.find(
                        (sub: any) =>
                          sub.dcfId === assignment.dcfId &&
                          sub.locationId === assignment.locationId && sub.level === assignment.level &&
                          formattedPeriods.every(fp => sub.reporting_period.includes(fp))
                      );

                      reportData.push({
                        "DCF Id": assignment.dcfId,
                        "Reporting Period": getRPTextFormat(formattedPeriods),
                        Status: "Pending for data submission",
                        type: submission?.type || null,
                        "Reporter": reporterNames,
                        "Reviewer": reviewerNames,
                        "Approver": approverNames,
                        "Entity": entity,
                        "Form": (assignment as any)?.dcf?.title || ''
                      });
                    }
                  }

                  const userMailMap: any = {};

                  reportData.forEach(({dcfId, Form, Reviewer, Approver, Entity, "Reporting Period": reporting_period, Status, type, Reporter = []}) => {
                    const keyInfo = {Form, Entity: Entity.name, "Reporting Period": reporting_period, Reporter: Reporter.map((x: any) => x.name), Reviewer: Reviewer.length ? Reviewer.map((x: any) => x.name) : 'Self', Approver: Approver.map((x: any) => x.name), Status};
                    if (!type) {
                      Reporter.forEach((user: any) => {
                        if (!userMailMap[user.email]) {
                          userMailMap[user.email] = {reporters: [], cc: [...new Set(['<EMAIL>', ...Approver.map((x: any) => x.email), ...Reviewer.map((x: any) => x.email)])], name: user.name};
                        }
                        userMailMap[user.email].cc = [...new Set([...userMailMap[user.email].cc, ...Approver.map((x: any) => x.email), ...Reviewer.map((x: any) => x.email)])];
                        userMailMap[user.email].reporters.push(keyInfo);
                      });
                    }

                    // Reviewer.forEach((user: any) => {
                    //   if (!userMailMap[user.email]) {
                    //     userMailMap[user.email] = {reporter: [], reviewer: []};
                    //   }
                    //   userMailMap[user.email].reviewer.push(keyInfo);
                    // });
                  });


                  mailArray.push({
                    id: i.id,
                    email: Object.entries(userMailMap).map(([email, value]) => {
                      const user = value as {name: string, reporters: any, cc: any}; // Explicit type assertion
                      return {
                        email,
                        cc: (i.id === 289 && email !== '<EMAIL>') ? user.cc : [],
                        name: user.name,
                        subject: 'Reminder to Submit the Assigned Data Collection Form(s) ',
                        body: `<div>
                                  <p>Dear ${user.name}</p>
                                  <p><strong>Reminder</strong> to complete your assigned <strong>Sustainability Data Collection Form(s)</strong> as per the submission timeline.</p>
<p>The following submissions are pending from your end and are essential for the completeness of our sustainability data reporting.</p>
${this.helper.generateHtmlTable(user.reporters)}
<p>Please log in to the ${userPortal ? `<a href=${userPortal}>EiSqr – ESG Platform </a>` : 'EiSqr – ESG User Platform'}  to complete and submit the required data along with all supporting documents before the deadline.</p>
<p>In case of any queries, raise a ticket or alternatively, write to us on  <a href="mailto:<EMAIL>" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>
                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
                                </div>`,
                      };
                    })
                  });


                }

              }


            }
          }
        }
      }
      return mailArray;
    } catch (err) {
      console.error(err);
      return [];
    }
  }
  @get('/send-reminder-reviewers', {
    responses: {
      '200': {
        description: 'Mail Ids',
      },
    },
  })

  async sendDCFReviewerSummary(): Promise<any> {
    const data = await this.filteredUP({where: {role: 'clientadmin', id: {inq: [28, 51]}}});
    const mailArray: any[] = [];

    try {
      for (const i of data) {
        if (
          i.role === 'clientadmin' &&
          i.information != null
        ) {

          const userPortal = i?.userPortalUrl || null
          const {reviewer_remainder_mail_date, reviewer_escalation_mail_date} = i.information;
          if (
            (reviewer_remainder_mail_date !== undefined &&
              parseInt(reviewer_remainder_mail_date) > 0 &&
              parseInt(reviewer_remainder_mail_date) <= 30) || (reviewer_escalation_mail_date !== undefined &&
                parseInt(reviewer_escalation_mail_date) > 0 &&
                parseInt(reviewer_escalation_mail_date) <= 30)
          ) {
            let currentDate = DateTime.utc().get('day');
            const currentMonth = DateTime.utc().get('month');
            const lastDayOfMonth = DateTime.utc().endOf('month').day;

            if (currentMonth === 2 && currentDate === lastDayOfMonth && (parseInt(reviewer_remainder_mail_date) >= lastDayOfMonth || parseInt(reviewer_escalation_mail_date) >= lastDayOfMonth)) {
              if (parseInt(reviewer_remainder_mail_date) >= lastDayOfMonth) {
                currentDate = reviewer_remainder_mail_date
              } else if (parseInt(reviewer_escalation_mail_date) >= lastDayOfMonth) {
                currentDate = reviewer_escalation_mail_date
              }
            }
            const count = (currentDate === parseInt(reviewer_escalation_mail_date)) ? 2 : 1

            if ((currentDate === parseInt(reviewer_remainder_mail_date)) || (currentDate === parseInt(reviewer_escalation_mail_date))) {
              const clientIndicatorAssignment = await this.userProfileRepository.assignDcfClients(i.id).find()
              const assignedData = clientIndicatorAssignment[clientIndicatorAssignment.length - 1]
              let assignedDcf: any = []
              const reportData: any[] = [];
              if (assignedData?.metric_ids?.length && assignedData?.topic_ids?.length) {
                const esgCategory = await this.newCategoryRepository.find({
                  include: [
                    {
                      relation: "newTopics",
                      scope: {
                        include: [{
                          relation: "newMetrics", scope: {
                            include: ["newDataPoints"],
                          }
                        }],
                      },
                    },
                  ],
                });
                const shapedCategory = esgCategory.map(item => {
                  if (item.newTopics) {
                    item.newTopics = item.newTopics.filter(topics =>
                      topics.newMetrics && topics.newMetrics.length > 0
                    );
                  }
                  return item;
                }).filter(item => item.newTopics && item.newTopics.length > 0)
                shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
                  if (assignedData.topic_ids && top.id && assignedData.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === i.id)) {
                    top.newMetrics.forEach((met) => {
                      if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0 && met.data1[0].source === 1) && met.id && assignedData.metric_ids && assignedData.metric_ids.includes(met.id) && (met.tag === null || parseFloat(met.tag) === i.id)) {
                        const overallDcfs = met?.newDataPoints?.flatMap((dp: any) => {
                          // Extract datasource and dcf_ids from each data point
                          const datasources = dp.data1?.map((d: any) => d.datasource).filter((ds: any) => ds != null) || [];
                          const dcfIds = dp.data1?.flatMap((d: any) => {
                            // Ensure dcf_ids is always an array
                            const dcfIdsValue = d.dcf_ids;
                            if (Array.isArray(dcfIdsValue)) {
                              return dcfIdsValue;
                            } else {
                              return []; // If not array (string, number, etc.), treat as empty array
                            }
                          }).filter((dcfId: any) => dcfId != null) || [];

                          // Combine both datasources and dcf_ids
                          return [...datasources, ...dcfIds];
                        })
                          .filter((dcfId: any) => dcfId) // Remove null/undefined values
                          .filter((v: any, i: any, a: any) => a.indexOf(v) === i); // Get unique values

                        if (overallDcfs) {
                          assignedDcf = Array.from(new Set([...assignedDcf, ...overallDcfs]));

                        }

                      }
                    })
                  }
                })

                const dcfEntityAssignment = await this.userProfileRepository.assignDcfEntities(i.id).find({where: {dcfId: {inq: assignedDcf}}})
                if (dcfEntityAssignment.length) {
                  const approverDCFdata = await this.getAssignedIndicatorList(i.id)
                  const locations = await this.userProfileRepository.locationOnes(i.id).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})
                  const locations0 = [0]
                  const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
                  const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locationMap: any = {
                    0: locations0,
                    1: locations1,
                    2: locations2,
                    3: locations3
                  };
                  const filteredAssignments = approverDCFdata.filter((assignment: any) => assignment.level === 3 ? ![278, 284].includes(assignment.locationId) : true).filter((assignment: any) => {
                    return dcfEntityAssignment.some((ent: any) => {
                      const tierKey = `tier${assignment.level}_ids`;
                      const validLocations = locationMap[assignment?.level || 0] || [];
                      const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
                        validLocations.includes(Number(id))
                      );
                      const isLocationMatch = ent.dcfId === assignment.dcfId && filteredTierIds.includes(Number(assignment.locationId));
                      return isLocationMatch;
                    });
                  }).filter((x: any) => x.reviewer_ids?.length)

                  const quantitativeSubmisisons = await this.userProfileRepository.quantitativeSubmissions(i.id).find({where: {dcfId: {inq: assignedDcf}}})
                  const reporter_ids = filteredAssignments.flatMap((d: any) => d?.reporter_ids || []);
                  const reviewer_ids = filteredAssignments.flatMap((d: any) => d?.reviewer_ids || []);
                  const approver_ids = filteredAssignments.flatMap((d: any) => d?.approver_ids || []);
                  const userList = await this.filteredUP({where: {id: {inq: [...reporter_ids, ...reviewer_ids, ...approver_ids]}}})


                  for (const assignment of filteredAssignments) {
                    const expectedPeriods = this.calculateReportingPeriods(
                      assignment.start_date,
                      assignment.end_date,
                      assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency, i.id === 289 ? 'Apr-2024' : ''
                    );


                    const [reporters, reviewers, approvers, entity] = await Promise.all([
                      this.getUsersByIds(assignment.reporter_ids ?? [], userList),
                      this.getUsersByIds(assignment.reviewer_ids ?? [], userList),
                      this.getUsersByIds(assignment.approver_ids ?? [], userList),
                      this.getSortedEntity(assignment.level, assignment.locationId, locations)
                    ]);

                    const reporterNames = reporters.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const reviewerNames = reviewers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const approverNames = approvers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    for (const periodGroup of expectedPeriods) {
                      const formattedPeriods = periodGroup.map(period => {
                        const [month, year] = period.split('-');
                        return `${month.padStart(2, '0')}-${year}`;
                      });

                      const submission = quantitativeSubmisisons.find(
                        (sub: any) =>
                          sub.dcfId === assignment.dcfId &&
                          sub.locationId === assignment.locationId && sub.level === assignment.level &&
                          formattedPeriods.every(fp => sub.reporting_period.includes(fp))
                      );

                      reportData.push({
                        "DCF Id": assignment.dcfId,
                        "Reporting Period": getRPTextFormat(formattedPeriods),
                        Status: "Pending  for data review",
                        type: submission?.type || null,
                        "Reporter": reporterNames,
                        "Reviewer": reviewerNames,
                        "Approver": approverNames,
                        "Entity": entity,
                        "Form": (assignment as any)?.dcf?.title || ''
                      });
                    }
                  }

                  const userMailMap: any = {};

                  reportData.forEach(({dcfId, Form, Reporter, Approver, Entity, "Reporting Period": reporting_period, Status, type, Reviewer = []}) => {
                    const keyInfo = {Form, Entity: Entity.name, "Reporting Period": reporting_period, Reporter: Reporter.map((x: any) => x.name), Reviewer: Reviewer.map((x: any) => x.name), Approver: Approver.map((x: any) => x.name), Status, };

                    if (type === 1) {
                      Reviewer.forEach((user: any) => {
                        if (!userMailMap[user.email]) {
                          userMailMap[user.email] = {reviewers: [], cc: [...new Set(['<EMAIL>', ...Approver.map((x: any) => x.email), ...Reporter.map((x: any) => x.email)])], name: user.name};
                        }
                        userMailMap[user.email].cc = [...new Set([...userMailMap[user.email].cc, ...Approver.map((x: any) => x.email), ...Reporter.map((x: any) => x.email)])];

                        userMailMap[user.email].reviewers.push(keyInfo);
                      });
                    }


                  });

                  mailArray.push({
                    id: i.id,
                    email: Object.entries(userMailMap).map(([email, value]) => {
                      const user = value as {name: string, reviewers: any, cc: any}; // Explicit type assertion
                      return {
                        email,
                        cc: (i.id === 289 && email !== '<EMAIL>') ? user.cc : [],
                        name: user.name,
                        subject: 'Reminder to Review Assigned Sustainability Data Collection Form(s)',
                        body: `<div> <p>Dear ${user.name}</p>
                                  <p><strong>Reminder</strong> to review the submitted data for your <strong>Sustainability Data Collection Form(s)</strong>  within the designated timeline.</p>
                                 <p>The following reviews are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>

                                  ${this.helper.generateHtmlTable(user.reviewers)}
                                  <p>Please log in to the ${userPortal ? `<a href=${userPortal}>EiSqr – ESG Platform</a>` : 'EiSqr – ESG User Platform'} to complete the review of the submitted data and supporting documents before the submission deadline.</p>
<p>In case of any queries, raise a ticket or alternatively, write to us on <a href="mailto:<EMAIL>" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>
                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
                                </div>`,
                      };
                    })
                  });


                }

              }


            }
          }
        }
      }
      return mailArray;
    } catch (err) {
      console.error(err);
      return [];
    }
  }
  @get('/send-reminder-approvers', {
    responses: {
      '200': {
        description: 'Mail Ids',
      },
    },
  })

  async sendDCFApproverSummary(): Promise<any> {
    const data = await this.filteredUP({where: {role: 'clientadmin', id: {inq: [28, 51]}}});
    const mailArray: any[] = [];

    try {
      for (const i of data) {
        if (
          i.role === 'clientadmin' &&
          i.information != null
        ) {

          const userPortal = i?.userPortalUrl || null
          const {approver_remainder_mail_date, approver_escalation_mail_date} = i.information;
          console.log(approver_escalation_mail_date, approver_remainder_mail_date)
          if (
            (approver_remainder_mail_date !== undefined &&
              parseInt(approver_remainder_mail_date) > 0 &&
              parseInt(approver_remainder_mail_date) <= 30) || (approver_escalation_mail_date !== undefined &&
                parseInt(approver_escalation_mail_date) > 0 &&
                parseInt(approver_escalation_mail_date) <= 30)
          ) {
            let currentDate = DateTime.utc().get('day');
            const currentMonth = DateTime.utc().get('month');
            const lastDayOfMonth = DateTime.utc().endOf('month').day;

            if (currentMonth === 2 && currentDate === lastDayOfMonth && (parseInt(approver_remainder_mail_date) >= lastDayOfMonth || parseInt(approver_escalation_mail_date) >= lastDayOfMonth)) {
              if (parseInt(approver_remainder_mail_date) >= lastDayOfMonth) {
                currentDate = approver_remainder_mail_date
              } else if (parseInt(approver_escalation_mail_date) >= lastDayOfMonth) {
                currentDate = approver_escalation_mail_date
              }
            }

            console.log(currentMonth, currentMonth)
            const count = (currentDate === parseInt(approver_escalation_mail_date)) ? 2 : 1
            if ((currentDate === parseInt(approver_remainder_mail_date)) || (currentDate === parseInt(approver_escalation_mail_date))) {
              const clientIndicatorAssignment = await this.userProfileRepository.assignDcfClients(i.id).find()

              const assignedData = clientIndicatorAssignment[clientIndicatorAssignment.length - 1]
              let assignedDcf: any = []
              const reportData: any[] = [];
              if (assignedData?.metric_ids?.length && assignedData?.topic_ids?.length) {
                const esgCategory = await this.newCategoryRepository.find({
                  include: [
                    {
                      relation: "newTopics",
                      scope: {
                        include: [{
                          relation: "newMetrics", scope: {
                            include: ["newDataPoints"],
                          }
                        }],
                      },
                    },
                  ],
                });
                const shapedCategory = esgCategory.map(item => {
                  if (item.newTopics) {
                    item.newTopics = item.newTopics.filter(topics =>
                      topics.newMetrics && topics.newMetrics.length > 0
                    );
                  }
                  return item;
                }).filter(item => item.newTopics && item.newTopics.length > 0)
                shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
                  if (assignedData.topic_ids && top.id && assignedData.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === i.id)) {
                    top.newMetrics.forEach((met) => {
                      if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0 && met.data1[0].source === 1) && met.id && assignedData.metric_ids && assignedData.metric_ids.includes(met.id) && (met.tag === null || parseFloat(met.tag) === i.id)) {
                        const overallDcfs = met?.newDataPoints?.flatMap((dp: any) => {
                          // Extract datasource and dcf_ids from each data point
                          const datasources = dp.data1?.map((d: any) => d.datasource).filter((ds: any) => ds != null) || [];
                          const dcfIds = dp.data1?.flatMap((d: any) => {
                            // Ensure dcf_ids is always an array
                            const dcfIdsValue = d.dcf_ids;
                            if (Array.isArray(dcfIdsValue)) {
                              return dcfIdsValue;
                            } else {
                              return []; // If not array (string, number, etc.), treat as empty array
                            }
                          }).filter((dcfId: any) => dcfId != null) || [];

                          // Combine both datasources and dcf_ids
                          return [...datasources, ...dcfIds];
                        })
                          .filter((dcfId: any) => dcfId) // Remove null/undefined values
                          .filter((v: any, i: any, a: any) => a.indexOf(v) === i); // Get unique values

                        if (overallDcfs) {
                          assignedDcf = Array.from(new Set([...assignedDcf, ...overallDcfs]));

                        }

                      }
                    })
                  }
                })

                const dcfEntityAssignment = await this.userProfileRepository.assignDcfEntities(i.id).find({where: {dcfId: {inq: assignedDcf}}})
                if (dcfEntityAssignment.length) {
                  const approverDCFdata = await this.getAssignedIndicatorList(i.id)
                  const entityAssignment = await this.userProfileRepository.assignDcfEntities(i.id).find()
                  const dcfUserAssignment = approverDCFdata.filter((assignment: any) => assignment.level === 3 ? ![278, 284].includes(assignment.locationId) : true).filter((item: any) =>
                    entityAssignment.some(({tier1_ids, tier2_ids, tier3_ids, tier0_ids, dcfId}) =>
                      dcfId === item.dcfId && ((item.tier1_id !== null && tier1_ids?.includes(item.tier1_id)) ||
                        (item.tier2_id !== null && tier2_ids?.includes(item.tier2_id)) ||
                        (item.tier3_id !== null && tier3_ids?.includes(item.tier3_id)) ||
                        (item.tier0_id !== null && tier0_ids?.includes(item.tier0_id)))
                    )
                  );
                  const locations = await this.userProfileRepository.locationOnes(i.id).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})
                  const locations0 = [0]
                  const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
                  const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locationMap: any = {
                    0: locations0,
                    1: locations1,
                    2: locations2,
                    3: locations3
                  };
                  const filteredAssignments = dcfUserAssignment.filter((assignment: any) => {
                    return dcfEntityAssignment.some((ent: any) => {
                      const tierKey = `tier${assignment.level}_ids`;
                      const validLocations = locationMap[assignment?.level || 0] || [];
                      const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
                        validLocations.includes(Number(id))
                      );
                      const isLocationMatch = ent.dcfId === assignment.dcfId && filteredTierIds.includes(Number(assignment.locationId));
                      return isLocationMatch;
                    });
                  });
                  const quantitativeSubmisisons = await this.userProfileRepository.quantitativeSubmissions(i.id).find({where: {dcfId: {inq: assignedDcf}}})

                  const reporter_ids = filteredAssignments.flatMap((d: any) => d?.reporter_ids || []);
                  const reviewer_ids = filteredAssignments.flatMap((d: any) => d?.reviewer_ids || []);
                  const approver_ids = filteredAssignments.flatMap((d: any) => d?.approver_ids || []);
                  const userList = await this.filteredUP({where: {id: {inq: [...reporter_ids, ...approver_ids, ...reviewer_ids]}}})


                  for (const assignment of filteredAssignments) {
                    const expectedPeriods = this.calculateReportingPeriods(
                      assignment.start_date,
                      assignment.end_date,
                      assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency, i.id === 289 ? 'Apr-2024' : ''
                    );


                    const [reporters, reviewers, approvers, entity] = await Promise.all([
                      this.getUsersByIds(assignment?.reporter_ids || [], userList),
                      this.getUsersByIds(assignment?.reviewer_ids || [], userList),
                      this.getUsersByIds(assignment?.approver_ids || [], userList),
                      this.getSortedEntity(assignment.level, assignment.locationId, locations)
                    ]);
                    const reporterNames = reporters.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const reviewerNames = reviewers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const approverNames = approvers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    for (const periodGroup of expectedPeriods) {
                      const formattedPeriods = periodGroup.map(period => {
                        const [month, year] = period.split('-');
                        return `${month.padStart(2, '0')}-${year}`;
                      });

                      const submission = quantitativeSubmisisons.find(
                        (sub: any) =>
                          sub.dcfId === assignment.dcfId &&
                          sub.locationId === assignment.locationId && sub.level === assignment.level &&
                          formattedPeriods.every(fp => sub.reporting_period.includes(fp))
                      );

                      // if (count === 2 ? this.helper.isNextMonthDay(getRPTextFormat(formattedPeriods) || '', approver_escalation_mail_date - 1) : true) {
                      reportData.push({
                        "DCF Id": assignment.dcfId,
                        "Reporting Period": getRPTextFormat(formattedPeriods),
                        Status: "Pending for data approval",
                        type: submission?.type || null,
                        "Reporter": reporterNames,
                        "Reviewer": reviewerNames,
                        "Approver": approverNames,
                        "Entity": entity,
                        "Form": (assignment as any)?.dcf?.title || ''
                      });
                      // }
                    }
                  }

                  const userMailMap: any = {};

                  reportData.forEach(({dcfId, Form, Entity, "Reporting Period": reporting_period, Status, Reporter, Reviewer, type, Approver = []}) => {


                    const keyInfo = {Form, Entity: Entity.name, "Reporting Period": reporting_period, Reporter: Reporter.map((x: any) => x.name), Reviewer: Reviewer.map((x: any) => x.name), Approver: Approver.map((x: any) => x.name), Status};
                    if (type === 2) {
                      Approver.forEach((user: any) => {
                        if (!userMailMap[user.email]) {
                          userMailMap[user.email] = {approvers: [], cc: [...new Set(['<EMAIL>', ...Reviewer.map((x: any) => x.email), ...Reporter.map((x: any) => x.email)])], name: user.name};
                        }
                        userMailMap[user.email].cc = [...new Set([...userMailMap[user.email].cc, ...Reviewer.map((x: any) => x.email), ...Reporter.map((x: any) => x.email)])];

                        userMailMap[user.email].approvers.push(keyInfo);
                      });
                    }

                  });

                  mailArray.push({
                    id: i.id,
                    email: Object.entries(userMailMap).map(([email, value]) => {
                      const user = value as {name: string, approvers: any, cc: any}; // Explicit type assertion
                      return {
                        email,
                        cc: (i.id === 289 && email !== '<EMAIL>') ? user.cc : [],
                        name: user.name,
                        subject: 'Reminder to Approve Assigned Sustainability Data Collection Form(s)',
                        body: `<div>
                                  <p>Dear ${user.name}</p>
                                  <p><strong>Reminder</strong> to complete the approval of your assigned <strong>Sustainability Data Collection Form(s)</strong> within the designated timeline.</p>
                                  <p>The following approval(s) are currently pending from your end and are critical for ensuring the completeness and accuracy of our sustainability data reporting.</p>
${this.helper.generateHtmlTable(user.approvers)}
<p>Please log in to the ${userPortal ? `<a href=${userPortal}>EiSqr – ESG Platform </a>` : 'EiSqr – ESG User Platform'} to approve the submitted data and associated supporting documents before the submission deadline.</p>

<p>In case of any queries, raise a ticket or alternatively, write to us on  <a href="mailto:<EMAIL>" > <EMAIL></a>. Thank you for your prompt attention to this matter.</p>
                                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
                                </div>`,
                      };
                    })
                  });


                }

              }


            }
          }
        }
      }
      return mailArray;
    } catch (err) {
      console.error(err);
      return [];
    }


  }
  @get('/send-reminder-esclation', {
    responses: {
      '200': {
        description: 'Mail Ids',
      },
    },
  })

  async sendDCFEsclationSummary(): Promise<any> {
    const data = await this.filteredUP({where: {role: 'clientadmin', id: {inq: [28, 51]}}});
    const mailArray: any[] = [];

    try {
      for (const i of data) {
        if (
          i.role === 'clientadmin' &&
          i.information != null
        ) {
          const others_escalation_mail_date = 10
          const others_escalation_mail_date_2 = 15
          const userPortal = i?.userPortalUrl || null
          const {escalation_mail_ids} = i.information;

          if (Array.isArray(escalation_mail_ids) && escalation_mail_ids.length) {
            let currentDate = DateTime.utc().get('day');
            const currentMonth = DateTime.utc().get('month');
            const lastDayOfMonth = DateTime.utc().endOf('month').day;

            if (currentMonth === 2 && currentDate === lastDayOfMonth && ((others_escalation_mail_date >= lastDayOfMonth) || (others_escalation_mail_date_2 >= lastDayOfMonth))) {
              if (others_escalation_mail_date >= lastDayOfMonth) {
                currentDate = others_escalation_mail_date
              } else if (others_escalation_mail_date_2 >= lastDayOfMonth) {
                currentDate = others_escalation_mail_date_2
              }
            }

            console.log(currentMonth, currentMonth)
            const count = currentDate === others_escalation_mail_date ? 1 : 2
            if ((currentDate === others_escalation_mail_date) || (currentDate === others_escalation_mail_date_2)) {
              const clientIndicatorAssignment = await this.userProfileRepository.assignDcfClients(i.id).find()

              const assignedData = clientIndicatorAssignment[clientIndicatorAssignment.length - 1]
              let assignedDcf: any = []
              const reportData: any[] = [];
              if (assignedData?.metric_ids?.length && assignedData?.topic_ids?.length) {
                const esgCategory = await this.newCategoryRepository.find({
                  include: [
                    {
                      relation: "newTopics",
                      scope: {
                        include: [{
                          relation: "newMetrics", scope: {
                            include: ["newDataPoints"],
                          }
                        }],
                      },
                    },
                  ],
                });
                const shapedCategory = esgCategory.map(item => {
                  if (item.newTopics) {
                    item.newTopics = item.newTopics.filter(topics =>
                      topics.newMetrics && topics.newMetrics.length > 0
                    );
                  }
                  return item;
                }).filter(item => item.newTopics && item.newTopics.length > 0)
                shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
                  if (assignedData.topic_ids && top.id && assignedData.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === i.id)) {
                    top.newMetrics.forEach((met) => {
                      if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0 && met.data1[0].source === 1) && met.id && assignedData.metric_ids && assignedData.metric_ids.includes(met.id) && (met.tag === null || parseFloat(met.tag) === i.id)) {
                        const overallDcfs = met?.newDataPoints?.flatMap((dp: any) => {
                          // Extract datasource and dcf_ids from each data point
                          const datasources = dp.data1?.map((d: any) => d.datasource).filter((ds: any) => ds != null) || [];
                          const dcfIds = dp.data1?.flatMap((d: any) => {
                            // Ensure dcf_ids is always an array
                            const dcfIdsValue = d.dcf_ids;
                            if (Array.isArray(dcfIdsValue)) {
                              return dcfIdsValue;
                            } else {
                              return []; // If not array (string, number, etc.), treat as empty array
                            }
                          }).filter((dcfId: any) => dcfId != null) || [];

                          // Combine both datasources and dcf_ids
                          return [...datasources, ...dcfIds];
                        })
                          .filter((dcfId: any) => dcfId) // Remove null/undefined values
                          .filter((v: any, i: any, a: any) => a.indexOf(v) === i); // Get unique values

                        if (overallDcfs) {
                          assignedDcf = Array.from(new Set([...assignedDcf, ...overallDcfs]));

                        }

                      }
                    })
                  }
                })

                const dcfEntityAssignment = await this.userProfileRepository.assignDcfEntities(i.id).find({where: {dcfId: {inq: assignedDcf}}})
                if (dcfEntityAssignment.length) {
                  const approverDCFdata = await this.getAssignedIndicatorList(i.id)
                  const entityAssignment = await this.userProfileRepository.assignDcfEntities(i.id).find()
                  const dcfUserAssignment = approverDCFdata.filter((item: any) =>
                    entityAssignment.some(({tier1_ids, tier2_ids, tier3_ids, tier0_ids, dcfId}) =>
                      dcfId === item.dcfId && ((item.tier1_id !== null && tier1_ids?.includes(item.tier1_id)) ||
                        (item.tier2_id !== null && tier2_ids?.includes(item.tier2_id)) ||
                        (item.tier3_id !== null && tier3_ids?.includes(item.tier3_id)) ||
                        (item.tier0_id !== null && tier0_ids?.includes(item.tier0_id)))
                    )
                  );
                  const locations = await this.userProfileRepository.locationOnes(i.id).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})
                  const locations0 = [0]
                  const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
                  const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
                  const locationMap: any = {
                    0: locations0,
                    1: locations1,
                    2: locations2,
                    3: locations3
                  };
                  const filteredAssignments = dcfUserAssignment.filter((assignment: any) => assignment.level === 3 ? ![278, 284].includes(assignment.locationId) : true).filter((assignment: any) => {
                    return dcfEntityAssignment.some((ent: any) => {
                      const tierKey = `tier${assignment.level}_ids`;
                      const validLocations = locationMap[assignment?.level || 0] || [];
                      const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
                        validLocations.includes(Number(id))
                      );
                      const isLocationMatch = ent.dcfId === assignment.dcfId && filteredTierIds.includes(Number(assignment.locationId));
                      return isLocationMatch;
                    });
                  });
                  const quantitativeSubmisisons = await this.userProfileRepository.quantitativeSubmissions(i.id).find({where: {dcfId: {inq: assignedDcf}}})
                  const approver_ids = filteredAssignments.flatMap((d: any) => d?.approver_ids || []);
                  const reviewer_ids = filteredAssignments.flatMap((d: any) => d?.reviewer_ids || []);
                  const reporter_ids = filteredAssignments.flatMap((d: any) => d?.reporter_ids || []);

                  const userList = await this.filteredUP({where: {id: {inq: [...reporter_ids, ...approver_ids, ...reviewer_ids]}}})


                  for (const assignment of filteredAssignments) {
                    const expectedPeriods = this.calculateReportingPeriods(
                      assignment.start_date,
                      assignment.end_date,
                      assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency, i.id === 289 ? 'Apr-2024' : ''
                    );


                    const [reporters, reviewers, approvers, entity] = await Promise.all([
                      this.getUsersByIds(assignment?.reporter_ids || [], userList),
                      this.getUsersByIds(assignment?.reviewer_ids || [], userList),
                      this.getUsersByIds(assignment?.approver_ids || [], userList),
                      this.getSortedEntity(assignment.level, assignment.locationId, locations)
                    ]);
                    if (!reporters.length) {
                      console.log(assignment.id)
                    }
                    const reporterNames = reporters.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const reviewerNames = reviewers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));
                    const approverNames = approvers.map((user: any) => ({
                      id: user.id,
                      name: user.information['empname'], email: user.email
                    }));


                    for (const periodGroup of expectedPeriods) {
                      const formattedPeriods = periodGroup.map(period => {
                        const [month, year] = period.split('-');
                        return `${month.padStart(2, '0')}-${year}`;
                      });

                      const submission = quantitativeSubmisisons.find(
                        (sub: any) =>
                          sub.dcfId === assignment.dcfId &&
                          sub.locationId === assignment.locationId && sub.level === assignment.level &&
                          formattedPeriods.every(fp => sub.reporting_period.includes(fp))
                      );
                      type Entity = {
                        name: string;
                        id: number
                      };
                      const frequency_list = [{name: 'Monthly', id: 1}, {name: 'Bi-Monthly', id: 2}, {name: 'Quarterly', id: 3}, {name: 'Annually', id: 4}, {name: 'Bi-Annually', id: 5}, {name: 'Undefined', id: 6}]

                      const entityName: Entity = entity as Entity;
                      if (count === 1 ? (!submission?.type || !submission) : true) {
                        reportData.push({
                          type: submission?.type || null,
                          "Data Collection Form": (assignment as any)?.dcf?.title || '',
                          "Reporting Entity ": entityName?.name ?? 'NA',
                          "Reporting Period": getRPTextFormat(formattedPeriods),
                          "Frequency": frequency_list.find(x => x.id === assignment.frequency)?.name || 'NA',
                          "Reporter": reporterNames.map(i => i.name).filter(x => x),
                          "Reviewer": assignment.reviewer_ids.length ? reviewerNames.map(i => i.name).filter(x => x) : 'Self',
                          "Approver": approverNames.map(i => i.name).filter(x => x),
                        });
                      }
                    }
                  }

                  const userMailMap: any = {};

                  mailArray.push(
                    {
                      id: i.id,
                      email: [{
                        "email": escalation_mail_ids,
                        name: "Sustainability Head",
                        subject: 'Escalation: Pending Data Submission/Review for Data Collection Forms',
                        body: `<div>
                                <p>Dear TVS Admin</p>
                                <p>This is to escalate the pending status of data submission and review for the following Data Collection Form(s): </p>
                                <p>Details of Pending Submissions/Reviews</p>
${this.helper.generateHtmlTable(reportData.filter(x => x.type < 3).map(({type, ...x}) => x))}
<p>Kindly review the Data Submission (Report/Review) Status and take appropriate action to ensure timely completion.</p>
<p> Thank you for addressing this matter promptly.</p>
                                <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
                              </div>`,


                      }]
                    })





                }

              }


            }
          }
        }
      }
      return mailArray;
    } catch (err) {
      console.error(err);
      return [];
    }


  }
  @get('/trigger-reporters-overdue', {
    responses: {
      '200': {
        description: 'Mail Ids',
      },
    },
  })
  async triggerReporterMails(): Promise<any> {
    const data = await this.userProfileRepository.find({where: {or: [{id: 17}, {id: 28}]}});
    const mailArray: any[] = [];

    try {
      for (const i of data) {
        if (
          i.role === 'clientadmin' &&
          i.information !== undefined &&
          i.information !== null
        ) {
          const {user_remainder_mail_date, user_escalation_mail_date} = i.information;
          if (
            (user_remainder_mail_date !== undefined &&
              parseInt(user_remainder_mail_date) > 0 &&
              parseInt(user_remainder_mail_date) <= 30) || (user_escalation_mail_date !== undefined &&
                parseInt(user_escalation_mail_date) > 0 &&
                parseInt(user_escalation_mail_date) <= 30)
          ) {
            const currentDate = DateTime.utc().get('day');
            ;
            if ((currentDate === parseInt(user_remainder_mail_date)) || (currentDate === parseInt(user_escalation_mail_date))) {
              const dcfAssignments = await this.userProfileRepository.assignDcfEntityUsers(i.id).find({
                include: ['dcf', 'lone', 'ltwo', 'lthree']
              });

              const userIds: number[] = [];

              const idList = await this.userProfileRepository.find({where: {id: {inq: userIds}}});
              const returnUsers = await Promise.all(idList.map(async (user) => {
                const userData = await this.userRepository.findById(user.userId);

                return {
                  email: userData.email, name: user.information.empname, subject: 'Submission Overdue Reminder  - NAVIGOS', body: `<div>
                <p>Hello, ${user.information.empname}</p>
                <p >Following is a list of submissions that are needed from your end for completeness of the sustainability data reporting. Please complete these at the earliest so that the can be further processed and included in the performance dashboards and reports as required.</p>
                <p>To know you overdue submissions  <a href="https://navigos.eisqr.com/#/submitter_overdue">click here</a></p>
                <p>This is an automated message. Please do not respond to this mail</p> </div>` };
              }))
              mailArray.push({id: i.id, email: returnUsers});
            }
          }
        }
      }
      return mailArray;
    } catch (err) {
      console.error(err);
      return [];
    }
  }
  @get('/stt-role-conversion')

  async manualChange(

  ): Promise<any> {
    const sttuser = await this.userProfileRepository.find({where: {clientId: 94, role: 'clientuser'}});

    const sttuser_ = await Promise.all(sttuser.map(async (data) => {
      if (data.information.role) {
        const info = data.information
        const response = {reviewer: data.information.role.viewer, approver: data.information.role.approver, viewer: false, reporter: data.information.role.reporter}
        info.role = response;
        await this.userProfileRepository.updateById(data.id, {information: info})

        data.information.role = response;
        return data;
      }

    }));

    return sttuser_;

  }
  @post('/change-mail-id')
  async changeMailId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {
                type: 'string',
              },
              date: {
                type: 'string',
              },
              email: {
                type: 'string',
              }
            },
            required: ['userId', 'date', 'email'],
          },
        },
      },
    })
    requestBody: {userId: string, email: string, date: string},
  ): Promise<any> {
    const {userId, date, email} = requestBody;
    const dt = DateTime.utc().toFormat('yyyy-LLL-dd')
    try {
      if (date === dt) {
        const uid = await this.userRepository.findOne({
          where: {id: userId}
        });
        if (uid) {

          return await this.userRepository.updateById(uid.id, {email})
        } else {
          throw new HttpErrors.BadRequest('User not exist');
        }
      }
      else {
        throw new HttpErrors.BadRequest('Invalid validation');
      }
    } catch (e) {

      throw new HttpErrors.BadRequest(e.message);
    }

  }

  @post('/delete-live-client-and-its-user-by-userids')
  async deleteUsersByuserId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {
                type: 'string',
              },
              date: {
                type: 'string',
              }
            },
            required: ['userId', 'date'],
          },
        },
      },
    })
    requestBody: {userId: string, date: string},
  ): Promise<any> {
    const {userId, date} = requestBody;
    const dt = DateTime.utc().toFormat('yyyy-LLL-dd')
    try {
      if (date === dt) {
        const uid = await this.userProfileRepository.findOne({
          where: {userId: userId}
        });

        if (uid && uid.role === 'clientadmin' && uid.id !== 94) {

          const users_list = await this.userProfileRepository.find({
            where: {clientId: uid.id}
          })
          const userIds = users_list.map(i => i.userId)
          if (!userIds.includes(uid.userId)) {
            userIds.push(uid.userId)
          }

          const deleteResult = await this.userRepository.deleteAll({
            id: {inq: userIds},
          });

          const deleteResult2 = await this.userProfileRepository.deleteAll({
            userId: {inq: userIds},
          });
          const deleteResult3 = await this.userCredentialsRepository.deleteAll({
            userId: {inq: userIds},
          });
          // 30
          this.userProfileRepository.assignDcfClients(uid.id).delete()
          this.userProfileRepository.assignDcfEntityUsers(uid.id).delete()
          this.userProfileRepository.assignDcfEntities(uid.id).delete()
          this.userProfileRepository.assignDcfSuppliers(uid.id).delete()
          this.userProfileRepository.assignDcfUserNews(uid.id).delete()
          this.userProfileRepository.assignDcfUsers(uid.id).delete()
          this.userProfileRepository.assignDfEntityUsers(uid.id).delete()
          this.userProfileRepository.assignDfEntities(uid.id).delete()
          this.userProfileRepository.assignDfUsers(uid.id).delete()
          this.userProfileRepository.assignRfEntities(uid.id).delete()
          this.userProfileRepository.assignRfUsers(uid.id).delete()
          this.userProfileRepository.assignSrfEntityUsers(uid.id).delete()
          this.userProfileRepository.assignSrfEntities(uid.id).delete()
          this.userProfileRepository.assignSrfUsers(uid.id).delete()
          this.userProfileRepository.clientEfCategoryAssignments(uid.id).delete()
          this.userProfileRepository.clientInitiatives(uid.id).delete()
          this.userProfileRepository.dpReportNews(uid.id).delete()
          this.userProfileRepository.dpReports(uid.id).delete()
          this.userProfileRepository.frequencies(uid.id).delete()
          this.userProfileRepository.locationOnes(uid.id).delete()
          this.userProfileRepository.newClientCertifications(uid.id).delete()
          this.userProfileRepository.newGoals(uid.id).delete()
          this.userProfileRepository.qlListingFilters(uid.id).delete()
          this.userProfileRepository.quantitativeDpReports(uid.id).delete()
          this.userProfileRepository.quantitativeSubmissions(uid.id).delete()
          this.userProfileRepository.submitCfs(uid.id).delete()
          this.userProfileRepository.submitDcfNews(uid.id).delete()
          this.userProfileRepository.submitDcfs(uid.id).delete()
          this.userProfileRepository.submitRfNews(uid.id).delete()
          this.userProfileRepository.submitRfs(uid.id).delete()


          return (deleteResult.count === deleteResult2.count && deleteResult2.count === deleteResult3.count)




        } else {

          throw new HttpErrors.BadRequest('User not found');
        }
      } else {
        throw new HttpErrors.BadRequest('Invalid validation');
      }


    } catch (e) {

      throw new HttpErrors.BadRequest(e.message);
    }

  }
  calculateReportingPeriods(
    startDate: any,
    endDate: any,
    frequency: any,
    minMonth: string = ''
  ): string[][] {
    const periods: string[][] = [];
    let currentDate = DateTime.fromISO(startDate, {zone: 'utc'}).plus({day: 1});
    const end = endDate ? DateTime.fromISO(endDate, {zone: 'utc'}).plus({day: 1}) : DateTime.utc();

    const now = DateTime.utc();
    const currentMonthKey = now.year * 100 + now.month;

    // Parse minMonth if provided (format: "Apr-2024" or "04-2024")
    let minMonthKey = 0;
    if (minMonth && minMonth.trim() !== '') {
      try {
        const minMonthParts = minMonth.split('-');
        if (minMonthParts.length === 2) {
          const [monthPart, yearPart] = minMonthParts;
          let monthNum: number;

          // Handle both "Apr-2024" and "04-2024" formats
          if (isNaN(parseInt(monthPart))) {
            // Month name format (e.g., "Apr")
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            monthNum = monthNames.indexOf(monthPart) + 1;
          } else {
            // Numeric format (e.g., "04")
            monthNum = parseInt(monthPart);
          }

          const yearNum = parseInt(yearPart);
          if (monthNum >= 1 && monthNum <= 12 && yearNum > 0) {
            minMonthKey = yearNum * 100 + monthNum;
          }
        }
      } catch (error) {
        console.warn(`Invalid minMonth format: ${minMonth}. Using default behavior.`);
      }
    }

    while (currentDate <= end) {
      const periodGroup: string[] = [];

      for (let i = 0; i < frequency && currentDate <= end; i++) {
        periodGroup.push(
          `${String(currentDate.month).padStart(2, '0')}-${currentDate.year}`
        );
        currentDate = currentDate.plus({months: 1});
      }

      // Ensure full group
      if (periodGroup.length < frequency) break;

      // For ALL frequencies → exclude group if last month is in future or current month
      const [lastMonthStr, lastYearStr] = periodGroup[periodGroup.length - 1].split('-');
      const lastGroupKey = parseInt(lastYearStr) * 100 + parseInt(lastMonthStr);

      // Only include periods that are strictly less than current month
      if (lastGroupKey >= currentMonthKey) continue;

      // If minMonth is specified, only include periods where the last month is >= minMonth
      if (minMonthKey > 0 && lastGroupKey < minMonthKey) continue;

      periods.push(periodGroup);
    }

    return periods;
  }

  async getUsersByIds(userIds: any, userList: any): Promise<any[]> {
    return userList.filter((x: any) => userIds.includes(x.id));
  }

  /**
   * Get reviewer name - if reviewed use actual reviewer, if not reviewed use assigned reviewer from assignment
   */
  private async getReviewerName(dataItem: any, assignedDcf: any[]): Promise<string> {
    try {
      // If already reviewed, use the actual reviewer
      if (dataItem.submitDcf?.type === 2) {
        if (dataItem.submitDcf?.self) {
          return "Self";
        }
        return `${await this.getUserByUPID(dataItem.submitDcf.reviewer_modified_by)}`;
      }

      // If not reviewed, get reviewer from assignment
      const assignment = assignedDcf.find((dcf: any) =>
        dcf.dcfId === dataItem.dcfId &&
        dcf.level === dataItem.submitDcf?.level &&
        dcf.locationId === dataItem.submitDcf?.locationId
      );

      if (assignment?.reviewer_ids && assignment?.reviewer_ids?.length > 0) {
        // Get the first reviewer from assignment
        const reviewerName = await this.filteredUP({where: {id: {inq: assignment.reviewer_ids}}})
        return reviewerName.map((x: any) => x?.information?.empname).join(',') || "Assigned Reviewer";
      }

      return assignment?.reviewer_ids?.length === 0 ? 'Self' : "N/A";
    } catch (error) {
      console.error('Error getting reviewer name:', error);
      return "N/A";
    }
  }

  /**
   * Get approver name - if approved use actual approver, if not approved use assigned approver from assignment
   */
  private async getApproverName(dataItem: any, assignedDcf: any[]): Promise<string> {
    try {
      // If already approved, use the actual approver
      if (dataItem.submitDcf?.type === 3) {
        return `${await this.getUserByUPID(dataItem.submitDcf.approved_by)}`;
      }

      // If not approved, get approver from assignment
      const assignment = assignedDcf.find((dcf: any) =>
        dcf.dcfId === dataItem.dcfId &&
        dcf.level === dataItem.submitDcf?.level &&
        dcf.locationId === dataItem.submitDcf?.locationId
      );

      if (assignment?.approver_ids && assignment.approver_ids.length > 0) {
        // Get the first approver from assignment

        const approverName = await this.filteredUP({where: {id: {inq: assignment.approver_ids}}})
        return approverName.map((x: any) => x?.information?.empname).join(',') || "Assigned Approver";
      }

      return "N/A";
    } catch (error) {
      console.error('Error getting approver name:', error);
      return "N/A";
    }
  }

  async getSortedEntity(level: any, locationId: any, locations: any) {


    const locationMap = new Map(
      locations.map((loc: any) => [
        loc.id,
        {id: loc.id, name: loc.name},
      ]),
    );

    const locationTwoMap = new Map(
      locations.flatMap(
        (loc: any) =>
          loc.locationTwos?.map((locTwo: any) => [
            locTwo.id,
            {id: locTwo.id, name: locTwo.name},
          ]) || [],
      ),
    );

    const locationThreeMap = new Map(
      locations.flatMap(
        (loc: any) =>
          loc.locationTwos?.flatMap(
            (locTwo: any) =>
              locTwo.locationThrees?.map((locThree: any) => [
                locThree.id,
                {id: locThree.id, name: locThree.name},
              ]) || [],
          ) || [],
      ),
    );

    return (
      (level === 0 && {id: 0, name: 'Corporate'}) ||
      (level === 1 && locationMap.get(locationId)) ||
      (level === 2 && locationTwoMap.get(locationId)) ||
      (level === 3 && locationThreeMap.get(locationId)) ||
      'No Data Found'
    );
  }

  generateRandomString() {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '123456789';
    const specialChars = '!#$*_+?';
    const allChars = uppercase + lowercase + numbers + specialChars;

    // Ensure that the string contains at least one of each required type
    const getRandomChar = (chars: any) => chars[Math.floor(Math.random() * chars.length)];
    let randomString = [
      getRandomChar(uppercase),
      getRandomChar(lowercase),
      getRandomChar(numbers),
      getRandomChar(specialChars),
    ];

    // Fill the rest of the string with random characters
    for (let i = 4; i < 8; i++) {
      randomString.push(getRandomChar(allChars));
    }

    // Shuffle the array to ensure the characters are in random positions
    randomString = randomString.sort(() => Math.random() - 0.5);

    // Join the array into a single string
    return randomString.join('');
  }

  @post('/reportBug')
  async reportBug(
    @requestBody() requestBody: {
      desc: string,
      type: string,
      accessToken: string,
    },
  ): Promise<void> {
    const {desc, type, accessToken} = requestBody;
    // data_submission
    // log in error
    // technical/others
    // none
    const data = {
      short_description: desc,
      description: desc,
      urgency: "2",
      impact: "3",
      category: "ESG",
      subcategory: "Submission",
      u_type: type
    };

    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    };

    const SERVICE_NOW_BUG_URL = `${process.env.SERVICE_BUG_URL}`; // Replace this with your actual URL

    try {
      const response = await axios.post(SERVICE_NOW_BUG_URL, data, config);
      return response.data
      // Implement additional logic based on response if needed
    } catch (error) {
      console.error('Error reporting the bug:', error);
      throw new HttpErrors.BadRequest('Failed to report bug due to an external service error');
    }
  }

  @post('/uploadToServiceNow', {
    responses: {
      200: {
        description: 'File uploaded to ServiceNow',
        content: {
          'application/json': {
            schema: {
              type: 'object',
            },
          },
        },
      },
    },
  })

  async uploadToServiceNow(
    @requestBody.file() request: Request,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<object> {
    // Call multer manually to handle file upload
    return new Promise<object>((resolve, reject) => {
      upload.single('file')(request, response, async (err) => {
        if (err) return reject(err);

        // The file is available in req.file, and other form fields are available in req.body
        const file = request.file;
        const incidentSysId = request.body.sys_id;

        if (!file) {
          reject('No file was uploaded.');
        }

        const formData = new FormData()
        formData.append('uploadFile', file?.buffer, file?.originalname);

        // Your ServiceNow instance details
        const serviceNowUrl = `${process.env.SERVICE_BASE_URL}/api/now/attachment/file?table_name=incident&table_sys_id=${incidentSysId}&file_name=${file?.originalname}`;

        // Securely get this from your environment variables or configuration
        const details: Details = {
          grant_type: 'password',
          client_id: `${process.env.SERVICE_CLIENT_ID}`,
          client_secret: `${process.env.SERVICE_CLIENT_SECRET}`,
          username: `${process.env.SERVICE_USERNAME}`,
          password: `${process.env.SERVICE_PASSWORD}`,
        };


        const formBody = Object.keys(details)
          .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(details[key]))
          .join('&');

        try {
          const response = await axios.post(`${process.env.SERVICE_URL}`, formBody, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          });
          const token = response.data.access_token;

          try {
            const serviceNowResponse = await axios.post(serviceNowUrl, formData, {
              headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${token}`
              },
              maxContentLength: Infinity,
              maxBodyLength: Infinity
            });

            resolve(serviceNowResponse.data);
          } catch (error) {
            reject(error);
          }
        } catch (error) {

          throw new HttpErrors.InternalServerError('Failed to obtain OAuth token');
        }


      });
    });
  }

  @get('/oauth-token')
  async getOAuthToken(): Promise<object> {
    const details: Details = {
      grant_type: 'password',
      client_id: `${process.env.SERVICE_CLIENT_ID}`,
      client_secret: `${process.env.SERVICE_CLIENT_SECRET}`,
      username: `${process.env.SERVICE_USERNAME}`,
      password: `${process.env.SERVICE_PASSWORD}`,
    };


    const formBody = Object.keys(details)
      .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(details[key]))
      .join('&');

    try {
      const response = await axios.post(`${process.env.SERVICE_URL}`, formBody, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      return response.data;
    } catch (error) {

      throw new HttpErrors.InternalServerError('Failed to obtain OAuth token');
    }
  }

  @post('/users/supplier', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': NewUserProfile,
            },
          },
        },
      },
    },
  })
  async createSupplier(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserProfile, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserProfile,
  ): Promise<any> {
    const {email, information, clientId, supplierCode, role, userId} = newUserRequest;
    let company_name = '', name = information?.supplierName, role_name = '', supplier_portal = ''
    const user_id = Number(userId)
    try {

      if (supplierCode && email) {
        const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
        if (!findVendorCode.length) {
          const tempPassword = this.generateRandomString()
          const data = await this.userProfileRepository.find({where: {id: clientId}})
          if (data.length) {
            company_name = data[0].information.companyname
            if (data[0].supplierPortalUrl) {
              supplier_portal = data[0].supplierPortalUrl
            }

            const findSupplier = await this.userRepository.findOne({where: {email}})
            if (!findSupplier) {
              const existingVendorWithCode = await this.vendorCodeRepository.findOne({
                where: {code: supplierCode}
              });

              if (existingVendorWithCode) {
                return {
                  status: false,
                  supplierCode,
                  message: `Vendor code '${supplierCode}' already exists. Please use a different code.`
                };
              }
              const savedUser = await this.userRepository.create(
                {email: email, username: supplierCode}
              );

              const password = await hash(tempPassword, await genSalt());
              await this.userRepository.userCredentials(savedUser.id).create({password: password});
              const newObj = {active: true, role: role, userId: savedUser.id, clientId, created_by: user_id}

              const createdUser = await this.userProfileRepository.create(newObj);
              if (createdUser) {
                // Check if vendor code already exists before creating


                const data = await this.vendorCodeRepository.create({...information, created_by: user_id, created_on: DateTime.utc().toString(), userProfileId: createdUser.id, code: supplierCode, clientId: clientId})
                return {status: true, message: 'Added new supplier with given vendor code', data: {...createdUser, information: data}}
              }
            } else {
              const findSupplierInfo = await this.userProfileRepository.findOne({where: {userId: findSupplier.id, role: 'clientsupplier'}})
              if (findSupplierInfo) {

                if (!findVendorCode.length) {
                  // Check if vendor code already exists before creating
                  const existingVendorWithCode = await this.vendorCodeRepository.findOne({
                    where: {code: supplierCode}
                  });

                  if (existingVendorWithCode) {
                    return {
                      status: false,
                      supplierCode,
                      message: `Vendor code '${supplierCode}' already exists. Please use a different code.`
                    };
                  }

                  const data = await this.vendorCodeRepository.create({...information, created_by: user_id, created_on: DateTime.utc().toString(), userProfileId: findSupplierInfo.id, code: supplierCode, clientId: clientId})
                  return {status: true, message: 'Added new Vendor Code for existing supplier', data: {...findSupplierInfo, information: data}}

                } else {
                  return {status: false, supplierCode, message: 'Supplier Detail with Vendor Code Already Exist, not able to add into platform'}
                }
              } else {
                return {status: false, supplierCode, message: 'Supplier Information not found in Navigos Platform'}

              }
            }
          } else {
            return {status: false, supplierCode, message: `Enterprise doesn't exist`}

          }
        } else {
          return {status: false, supplierCode, message: 'Supplier code already assigned  to another supplier'}

        }
      }
      else {
        return {status: false, supplierCode, message: 'Supplier code is required'}
      }
    } catch (error) {


      return {status: false, supplierCode, message: "Something went wrong while creating supplier account"}

    }
  }

  @post('/users/dealer', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': NewUserProfile,
            },
          },
        },
      },
    },
  })
  async createDealer(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserProfile, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserProfile,
  ): Promise<any> {
    const {email, information, clientId, supplierCode, role, userId} = newUserRequest;
    let company_name = '', name = information?.supplierName, role_name = '', supplier_portal = ''
    const user_id = Number(userId)
    try {

      if (supplierCode && email && typeof user_id === 'number') {
        const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
        if (!findVendorCode.length) {
          const tempPassword = this.generateRandomString()
          const data = await this.userProfileRepository.find({where: {id: clientId}})
          if (data.length) {
            company_name = data[0].information.companyname
            if (data[0].supplierPortalUrl) {
              supplier_portal = data[0].supplierPortalUrl
            }

            const findSupplier = await this.userRepository.findOne({where: {email}})
            if (!findSupplier) {
              const savedUser = await this.userRepository.create(
                {email: email, username: supplierCode}
              );

              const password = await hash(tempPassword, await genSalt());
              await this.userRepository.userCredentials(savedUser.id).create({password: password});
              const newObj = {active: true, role: role, userId: savedUser.id, clientId, created_by: user_id}

              const createdUser = await this.userProfileRepository.create(newObj);
              if (createdUser) {
                const data = await this.vendorCodeRepository.create({...information, created_by: user_id, created_on: DateTime.utc().toString(), userProfileId: createdUser.id, code: supplierCode, clientId: clientId})
                return {status: true, message: 'Added new dealer with given vendor code', supplierCode, data: {...createdUser, information: data}}
              }
            } else {
              const findSupplierInfo = await this.userProfileRepository.findOne({where: {userId: findSupplier.id, role: 'clientdealer'}})
              if (findSupplierInfo) {

                if (!findVendorCode.length) {
                  const data = await this.vendorCodeRepository.create({...information, created_by: user_id, created_on: DateTime.utc().toString(), userProfileId: findSupplierInfo.id, code: supplierCode, clientId: clientId})
                  return {status: true, message: 'Added new Vendor Code for existing dealer', supplierCode, data: {...findSupplierInfo, information: data}}

                } else {
                  return {status: false, message: 'Dealer Detail with Vendor Code Already Exist, not able to add into platform', supplierCode}
                }
              } else {
                return {status: false, message: 'Dealer Information not found in Navigos Platform', supplierCode}

              }
            }
          } else {
            return {status: false, message: `Enterprise doesn't exist`, supplierCode}

          }
        } else {
          return {status: false, message: 'Dealer code already assigned  to another supplier', supplierCode}

        }
      }
      else {
        return {status: false, message: 'Dealer code is required', supplierCode}
      }
    } catch (error) {

      return {status: false, message: "Something went wrong while creating dealer account", supplierCode}

    }
  }

  @post('/users/external/supplier-new', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': NewUserProfile,
            },
          },
        },
      },
    },
  })
  async createExternalNew(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserProfile, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserProfile,
  ): Promise<any> {
    const {email, information, clientId, supplierCode, role} = newUserRequest;
    let company_name = '', name = information?.supplierName, role_name = '', supplier_portal = ''

    try {

      if (supplierCode) {
        const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
        if (!findVendorCode.length) {
          const tempPassword = 'Supplier@' + supplierCode

          const params = {
            UserPoolId: `${process.env.AWS_TVS_SUPPLIER_POOL}`,
            Username: supplierCode,
            TemporaryPassword: 'Supplier@' + supplierCode,
            MessageAction: 'SUPPRESS',
            UserAttributes: [
              {
                Name: 'email',
                Value: newUserRequest.email
              },
              {
                Name: 'email_verified',
                Value: 'true'
              }
            ]
          }

          const awsNewUser = await cognito2.adminCreateUser(params).promise();

          if (awsNewUser.User?.Username) {
            const data = await this.userProfileRepository.find({where: {id: clientId}})

            if (data.length) {

              company_name = data[0].information.companyname
              if (data[0].supplierPortalUrl) {
                supplier_portal = data[0].supplierPortalUrl
              }

            }
            const savedUser = await this.userRepository.create(
              {email: email, username: supplierCode}
            );

            const password = await hash(tempPassword, await genSalt());
            await this.userRepository.userCredentials(savedUser.id).create({password: password});
            const newObj = {active: true, role: role, userId: savedUser.id, clientId, cognitoRefUserName: supplierCode}

            const createdUser = await this.userProfileRepository.create(newObj);
            if (createdUser) {

              const data = await this.vendorCodeRepository.create({...information, userProfileId: createdUser.id, code: supplierCode, clientId: clientId})
              return {status: true, message: 'Added new supplier with given vendor code', data: {...createdUser, information: data}}


              const body = `<p>Dear ${name},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform. As a ${company_name} , you have been assigned the role of  <strong>Supplier</strong> </p><p>Supplier Portal: <a href=${supplier_portal}>${supplier_portal}</a>  </p>  <p>Please find below your login credentials: </p><p>Username/Email: <strong>${email}</strong></p><p> Temporary Password: <strong>${tempPassword} </strong></p><p>  Upon initial login, we recommend to changing your password for security purposes.</p><p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

              // this.sqsService.sendEmail(email, 'NAVIGOS SUPPLIER ACCOUNT CREATION', body).then((info) => {
              //
              //   return info
              // }).catch((err) => {
              //
              //   return {status: false, message: err.errmsg}
              // })

            }




          } else {
            return {status: false, message: 'Failed to create supplier account'}
          }

        } else {
          return {status: false, message: 'Supplier code already assigned  to another supplier'}

        }
      }
      else {
        return {status: false, message: 'Supplier code is required'}
      }
    } catch (error) {
      // const tempPassword = 'Supplier@' + supplierCode
      // const findSupplier = await this.userRepository.findOne({where: {email}})
      // if (findSupplier && supplierCode) {
      //   const findSupplierInfo = await this.userProfileRepository.findOne({where: {userId: findSupplier.id}})
      //   if (findSupplierInfo) {
      //     const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
      //     if (!findVendorCode.length) {
      //       const data = await this.vendorCodeRepository.create({...information, userProfileId: findSupplierInfo.id, code: supplierCode, clientId: clientId})
      //       return {status: true, message: 'Added new Vendor Code for existing supplier', data: {...findSupplierInfo, information: data}}

      //     } else {
      //       return {status: false, message: 'Supplier Detail with Vendor Code Already Exist, not able to add into platform'}
      //     }
      //   } else {
      //     return {status: false, message: 'Supplier Information not found in Navigos Platform'}

      //   }
      // } else if (supplierCode) {
      //   const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
      //   if (!findVendorCode.length) {
      //     const data = await this.userProfileRepository.find({where: {id: clientId}})
      //
      //     if (data.length) {

      //       company_name = data[0].information.companyname
      //       if (data[0].supplierPortalUrl) {
      //         supplier_portal = data[0].supplierPortalUrl
      //       }

      //     }
      //     const savedUser = await this.userRepository.create(
      //       {email: email, username: email}
      //     );
      //
      //     const password = await hash(tempPassword, await genSalt());
      //     await this.userRepository.userCredentials(savedUser.id).create({password: password});
      //     let newObj = {active: true, role: role, userId: savedUser.id, clientId, cognitoRefUserName: email}
      //     try {
      //       const createdUser = await this.userProfileRepository.create(newObj);
      //       if (createdUser && supplierCode) {
      //         if (!findVendorCode.length) {
      //           const data = await this.vendorCodeRepository.create({...information, userProfileId: createdUser.id, code: supplierCode, clientId: clientId})
      //           return {status: true, message: 'Added new supplier with given vendor code', data: {...createdUser, information: data}}

      //         } else {
      //           return {status: true, message: 'Supplier Created Successfully but Vendor Code is already assigned with other supplier', data: {...createdUser, information: data}}
      //         }
      //       }
      //     } catch (e) {

      //       const findSupplier = await this.userRepository.findOne({where: {email}})
      //       if (findSupplier && supplierCode) {
      //         const findSupplierInfo = await this.userProfileRepository.findOne({where: {userId: findSupplier.id}})
      //         if (findSupplierInfo) {
      //           const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
      //           if (!findVendorCode.length) {
      //             const data = await this.vendorCodeRepository.create({...information, userProfileId: findSupplierInfo.id, code: supplierCode, clientId: clientId})


      //             return {status: true, message: 'Added new Vendor Code for existing supplier', data: {...findSupplierInfo, information: data}}

      //           } else {
      //             return {status: false, message: 'Supplier Detail with Vendor Code Already Exist, not able to add into platform'}
      //           }
      //         } else {
      //           return {status: false, message: 'Supplier Information not found in Navigos Platform'}

      //         }
      //       } else {
      //         return {status: false, message: 'Supplier Email not found in Navigos Platform', code: 3}

      //       }

      //     }
      //   } else {
      //     return {status: false, message: 'Supplier code already assigned  to another supplier'}

      //   }
      // }
      // MongoError 11000 duplicate key
      if ((error.code === 11000 || error.code === 'UsernameExistsException')) {
        const tempPassword = 'Supplier@' + supplierCode
        const findSupplier = await this.userRepository.findOne({where: {email}})
        if (findSupplier && supplierCode) {
          const findSupplierInfo = await this.userProfileRepository.findOne({where: {userId: findSupplier.id, role: 'clientsupplier'}})
          if (findSupplierInfo) {
            const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
            if (!findVendorCode.length) {
              const data = await this.vendorCodeRepository.create({...information, userProfileId: findSupplierInfo.id, code: supplierCode, clientId: clientId})
              return {status: true, message: 'Added new Vendor Code for existing supplier', data: {...findSupplierInfo, information: data}}

            } else {
              return {status: false, message: 'Supplier Detail with Vendor Code Already Exist, not able to add into platform'}
            }
          } else {
            return {status: false, message: 'Supplier Information not found in Navigos Platform'}

          }
        } else if (!findSupplier && supplierCode) {
          const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
          if (!findVendorCode.length) {
            const data = await this.userProfileRepository.find({where: {id: clientId}})

            if (data.length) {

              company_name = data[0].information.companyname
              if (data[0].supplierPortalUrl) {
                supplier_portal = data[0].supplierPortalUrl
              }

            }
            const savedUser = await this.userRepository.create(
              {email: email, username: supplierCode}
            );

            const password = await hash(tempPassword, await genSalt());
            await this.userRepository.userCredentials(savedUser.id).create({password: password});
            const newObj = {active: true, role: role, userId: savedUser.id, clientId, cognitoRefUserName: supplierCode}

            const createdUser = await this.userProfileRepository.create(newObj);
            if (createdUser) {

              const data = await this.vendorCodeRepository.create({...information, userProfileId: createdUser.id, code: supplierCode, clientId: clientId})
              return {status: true, message: 'Added new supplier with given vendor code', data: {...createdUser, information: data}}


              const body = `<p>Dear ${name},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform. As a ${company_name} , you have been assigned the role of  <strong>Supplier</strong> </p><p>Supplier Portal: <a href=${supplier_portal}>${supplier_portal}</a>  </p>  <p>Please find below your login credentials: </p><p>Username/Email: <strong>${email}</strong></p><p> Temporary Password: <strong>${tempPassword} </strong></p><p>  Upon initial login, we recommend to changing your password for security purposes.</p><p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

              // this.sqsService.sendEmail(email, 'NAVIGOS SUPPLIER ACCOUNT CREATION', body).then((info) => {
              //
              //   return info
              // }).catch((err) => {
              //
              //   return {status: false, message: err.errmsg}
              // })

            }
          } else {
            return {status: false, message: 'Supplier code already assigned  to another supplier'}

          }
        } else {
          return {status: false, message: 'Email value is already taken'}
        }



      } else {
        const findSupplier = await this.userRepository.findOne({where: {email}})
        if (findSupplier && supplierCode) {
          const findSupplierInfo = await this.userProfileRepository.findOne({where: {userId: findSupplier.id, role: 'clientsupplier'}})
          if (findSupplierInfo) {
            const findVendorCode = await this.vendorCodeRepository.find({where: {code: supplierCode, clientId}})
            if (!findVendorCode.length) {
              const data = await this.vendorCodeRepository.create({...information, userProfileId: findSupplierInfo.id, code: supplierCode, clientId: clientId})
              return {status: true, message: 'Added new Vendor Code for existing supplier', data: {...findSupplierInfo, information: data}}

            } else {
              return {status: false, message: 'Supplier Detail with Vendor Code Already Exist, not able to add into platform'}
            }
          } else {
            return {status: false, message: 'Supplier Information not found in Navigos Platform'}

          }
        }
        return {status: false, message: "Something went wrong while creating supplier account"}
      }
    }
  }

  @post('/users/dealer-multiple')
  @response(200, {
    description: 'Array of objects without a model reference'

  })
  async createMultipleExternalDealer(
    @requestBody() dealerArray: any[],
  ): Promise<any> {
    const addedSupplier = [], rejectedSupplier = []
    if (dealerArray && dealerArray.length) {
      for (const item of dealerArray) {
        const result = await this.createDealer(item)

        if (result.status) {
          addedSupplier.push(result.data)
        } else {
          rejectedSupplier.push({"Branch Code": result.supplierCode, Remarks: result.message})
        }
      }
    }

    return {status: (dealerArray.length && (addedSupplier.length === dealerArray.length)), addedSupplier, rejectedSupplier}

  }
  @post('/users/supplier-multiple')
  @response(200, {
    description: 'Array of objects without a model reference'

  })
  async createMultipleExternalSupplier(
    @requestBody() dealerArray: any[],
  ): Promise<any> {
    const addedSupplier = [], rejectedSupplier = []
    if (dealerArray && dealerArray.length) {
      for (const item of dealerArray) {
        const result = await this.createSupplier(item)

        if (result.status) {
          addedSupplier.push(result.data)
        } else {
          rejectedSupplier.push({"Vendor Code": result.supplierCode, Remarks: result.message})
        }
      }
    }

    return {status: (dealerArray.length && (addedSupplier.length === dealerArray.length)), addedSupplier, rejectedSupplier}

  }
  @post('/resend-password-upid')
  async reSendByUPIdPassword(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {
                type: 'number',
              }
            },
            required: ['userId'],
          },
        },
      },
    })
    requestBody: {userId: number},
  ): Promise<any> {
    const {userId} = requestBody;

    try {
      const newPassword = this.generateRandomString()
      const passwordHash = await hash(newPassword, await genSalt());
      const userObj = await this.userProfileRepository.findById(userId);
      const userData = await this.userRepository.findById(userObj.userId);

      await this.userRepository
        .userCredentials(userData.id)
        .patch({password: passwordHash});

      // Remove reset key from database its no longer valid
      userData.verificationToken = '';

      // Update the user removing the reset key
      await this.userRepository.updateById(userData.id, userData);
      const to = userData.email
      const params = {
        UserPoolId: `${process.env.REACT_APP_TVS_AWS_EXTERNAL_USER_POOL_ID}`,
        Username: to,
        Password: newPassword,
        Permanent: true
      };
      await cognitoTVSExternal.adminSetUserPassword(params).promise();
      const subject = 'Navigos Account Password Reset Notification'
      const body = `<p>Dear User,</p><p>We would like to inform you that your password for the  Navigos Platform has been reset by the admin.</p><p>Portal Url : <a href="https://tvsmotor.eisqr.com">https://tvsmotor.eisqr.com</a>  </p>   <p>Please find below your login credentials: </p><p>Login Id: <strong>${userData?.email}</strong></p><p> New Password: <strong>${newPassword} </strong></p>  <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p> <p>
Best regards,Navigos Team</p> <p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

      const info = await this.sqsService.sendEmail(to, subject, body, []).then((info) => {

        return info
      }).catch((err) => {

        return {status: false, message: err.errmsg}
      })
      return {status: true, message: info}
    } catch (error) {

      return {status: false, message: "E Mail Id already exist or something went wrong, please <NAME_EMAIL> for clarification"}


    }
  }
  @post('/resend-supplier-mail')
  async reSendSupplierPassword(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {
                type: 'number',
              },
              vendorId: {
                type: 'number',
              }
            },
            required: ['userId', 'vendorId'],
          },
        },
      },
    })
    requestBody: {userId: number, vendorId: number},
  ): Promise<any> {
    const {userId, vendorId} = requestBody;

    try {
      const newPassword = this.generateRandomString()
      const passwordHash = await hash(newPassword, await genSalt());
      const userObj = await this.userProfileRepository.findById(userId);
      const adminObj = await this.userProfileRepository.findById(userObj.clientId);
      const userData = await this.userRepository.findById(userObj.userId);
      const vendor = await this.vendorCodeRepository.findById(vendorId);

      await this.userRepository
        .userCredentials(userData.id)
        .patch({password: passwordHash});

      // Remove reset key from database its no longer valid
      userData.verificationToken = '';

      // Update the user removing the reset key
      await this.userRepository.updateById(userData.id, userData);
      const to = userData.email
      const counter = userObj?.emailSentCount ? userObj.emailSentCount + 1 : 1

      const subject = userObj?.emailSentCount ? 'TVSM Navigos Account Password Reset Notification' : 'NAVIGOS SUPPLIER ACCOUNT CREATION - TVSM'
      const allVendor = await this.vendorCodeRepository.find({where: {userProfileId: userId}})

      let body = `<p>Dear ${vendor?.supplierName || 'Supplier'},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p><p>  Upon initial login, we recommend to changing your password (use forgot password option on the login screen) for security purposes.</p> <p>Please note that ${userData?.email} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
      const vendorDetails = this.getUniqueValidEmails(allVendor)

      if (counter > 1) {

        if (vendorDetails.length) {
          for (const element of vendorDetails) {
            for (const spocmail of element.emails) {
              body = `<p>Dear ${spocmail},</p><p>We would like to inform you that your password for the TVSM's Navigos Platform has been reset by the TVSM admin.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>Use your Vendor Code as the Login ID</strong></p><p> New Password: <strong>${newPassword} </strong></p><p>If you need to access multiple accounts from your organization, please use the respective Vendor Code as the Login ID for each account.</p><p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p> <p>
Best regards,TVSM Support Team</p> <p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
              const info = await this.sqsService.sendEmail(spocmail, subject, body, []).then((info) => {

                return info
              }).catch((err) => {

                return {status: false, message: err.errmsg}
              })
            }

          }

        }
      } else {
        if (vendorDetails.length) {
          for (const element of vendorDetails) {
            for (const spocmail of element.emails) {
              body = `<p>Dear ${spocmail},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>Use your Vendor Code as the Login ID</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p> <p>Please note that ${userData?.email} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p><p>If you need to access multiple accounts from your organization, please use the respective Vendor Code as the Login ID for each account.</p><p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

              const info = await this.sqsService.sendEmail(spocmail, subject, body, []).then((info) => {

                return info
              }).catch((err) => {

                return {status: false, message: err.errmsg}
              })
            }

          }

        }
      }

      if (counter > 1) {
        body = `<p>Dear Supplier,</p><p>We would like to inform you that your password for the TVSM's Navigos Platform has been reset by the TVSM admin.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> New Password: <strong>${newPassword} </strong></p><p> For security reasons, we recommend changing your password upon first login (use the 'Forgot Password' option on the login screen).</p>  <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p> <p>
Best regards,TVSM Support Team</p> <p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

      } else {
        body = `<p>Dear Supplier,</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p><p>  Upon initial login, we recommend to changing your password (use forgot password option on the login screen) for security purposes.</p> <p>Please note that ${userData?.email} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

      }
      await this.userProfileRepository.updateById(userObj.id, {emailSentCount: counter});

      const info = await this.sqsService.sendEmail(to, subject, body, []).then((info) => {

        return info
      }).catch((err) => {

        return {status: false, message: err.errmsg}
      })
      return {status: false, message: info}
    } catch (error) {

      return {status: false, message: "E Mail Id already exist or something went wrong, please <NAME_EMAIL> for clarification"}


    }
  }
  @post('/resend-dealer-mail')
  async reSendDealerPassword(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {
                type: 'number',
              },
              vendorId: {
                type: 'number',
              }
            },
            required: ['userId', 'vendorId'],
          },
        },
      },
    })
    requestBody: {userId: number, vendorId: number},
  ): Promise<any> {
    const {userId, vendorId} = requestBody;

    try {
      const newPassword = this.generateRandomString()
      const passwordHash = await hash(newPassword, await genSalt());
      const userObj = await this.userProfileRepository.findById(userId);
      const adminObj = await this.userProfileRepository.findById(userObj.clientId);
      const userData = await this.userRepository.findById(userObj.userId);
      const vendor = await this.vendorCodeRepository.findById(vendorId)

      await this.userRepository
        .userCredentials(userData.id)
        .patch({password: passwordHash});

      // Remove reset key from database its no longer valid
      userData.verificationToken = '';

      // Update the user removing the reset key
      await this.userRepository.updateById(userData.id, userData);
      const to = userData.email
      const counter = userObj?.emailSentCount ? userObj?.emailSentCount + 1 : 1
      const subject = userObj?.emailSentCount ? 'TVSM Navigos Account Password Reset Notification' : 'NAVIGOS Dealer Account Creation - TVSM'
      const allVendor = await this.vendorCodeRepository.find({where: {userProfileId: userId}})

      let body = `<p>Dear Dealer,</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Dealer.</p><p>Dealer Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p><p>  Upon initial login, we recommend to changing your password (use forgot password option on the login screen) for security purposes.</p> <p>Please note that ${userData?.email} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`


      if (counter > 1) {
        body = `<p>Dear Dealer,</p><p>We would like to inform you that your password for the TVSM's Navigos Platform has been reset by the TVSM admin.</p><p>Dealer Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> New Password: <strong>${newPassword} </strong></p><p> For security reasons, we recommend changing your password upon first login (use the 'Forgot Password' option on the login screen).</p>  <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p> <p>
      Best regards,TVSM Support Team</p> <p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
      }


      this.userProfileRepository.updateById(userObj.id, {emailSentCount: counter})

      const info = await this.sqsService.sendEmail(to, subject, body, []).then((info) => {
        console.log('mail sent')
        return info
      }).catch((err) => {
        console.log('error in sending')

        return {status: false, message: err.errmsg}
      })
      return {status: true, message: 'success'}
    } catch (error) {


      return {status: false, message: error.errmsg}

    }
  }
  @post('/change-spocmail-id')
  async changeSPOCMailId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {
                type: 'number',
              },
              role: {
                type: 'string',
              },
              email: {
                type: 'string',
              }
            },
            required: ['userProfileId', 'email', 'role'],
          },
        },
      },
    })
    requestBody: {userProfileId: number, email: string, role: string},
  ): Promise<any> {
    const {userProfileId, email, role} = requestBody;

    try {
      const userId = await this.userProfileRepository.findOne({where: {id: userProfileId, role}});
      if (userId) {

        const uid = await this.userRepository.findOne({
          where: {id: userId.userId}
        });
        if (uid) {


          const newPassword = this.generateRandomString()
          const passwordHash = await hash(newPassword, await genSalt());
          const adminObj = await this.userProfileRepository.findById(userId.clientId);



          await this.userRepository
            .userCredentials(uid.id)
            .patch({password: passwordHash});

          // Remove reset key from database its no longer valid
          uid.verificationToken = '';

          // Update the user removing the reset key
          await this.userRepository.updateById(uid.id, {...uid, email})
          const counter = userId?.emailSentCount ? userId.emailSentCount + 1 : 1

          const subject = 'NAVIGOS Supplier Account Creation - TVSM'
          const allVendor = await this.vendorCodeRepository.find({where: {userProfileId: userId.id}})
          const vendorDetails = this.getUniqueValidEmails(allVendor)
          let body = `<p>Dear Supplier,</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p><p>  Upon initial login, we recommend to changing your password (use forgot password option on the login screen) for security purposes.</p> <p>Please note that ${email} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

          if (vendorDetails.length) {
            for (const element of vendorDetails) {
              for (const spocmail of element.emails) {

                body = `<p>Dear ${spocmail},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${element.code}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p> <p>Please note that ${email} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`

                const info = await this.sqsService.sendEmail(spocmail, subject, body, []).then((info) => {

                  return info
                }).catch((err) => {

                  return {status: false, message: err.errmsg}
                })
              }

            }

          }
          body = `<p>Dear Supplier,</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p> <p></p>Login ID(s) have been provided based on the respective Vendor Code(s). Please use the specific Login ID corresponding to your Vendor Code to access the Navigos platform. <p>  Upon initial login, we recommend to changing your password (use forgot password option on the login screen) for security purposes.</p>  <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
          await this.userProfileRepository.updateById(userId.id, {emailSentCount: counter});

          const info = await this.sqsService.sendEmail(email, subject, body, []).then((info) => {

            return info
          }).catch((err) => {

            return {status: false, message: err.errmsg}
          })
          return {status: true, message: "SPOC Mail Id changed successfully"}

        } else {
          return {status: false, message: "Email Id doesn't exist in platform"}

        }
      } else {
        return {status: false, message: "User information doesn't exist in platform"}

      }

    } catch (e) {

      return {status: false, message: "E Mail Id already exist or something went wrong, please <NAME_EMAIL> for clarification"}
    }

  }


  @post('/change-vendor-spocmail-id')
  async changeSupplierSpocMailId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              vendorId: {
                type: 'number',
              },
              newEmail: {
                type: 'string',
                format: 'email',
              },
              role: {
                type: 'string',
              },
              requesterId: {
                type: 'number',
              },
              createIfNotExists: {
                type: 'boolean',
                default: false,
              }
            },
            required: ['vendorId', 'newEmail', 'role', 'requesterId'],
          },
        },
      },
    })
    requestBody: {vendorId: number, newEmail: string, role: string, requesterId: number, createIfNotExists?: boolean},
  ): Promise<any> {
    const {vendorId, newEmail, role, requesterId, createIfNotExists = false} = requestBody;

    try {
      // Get the vendor details to find current email
      const vendor = await this.vendorCodeRepository.findById(vendorId);
      if (!vendor) {
        return {
          status: false,
          message: "Vendor not found"
        };
      }

      // Get current user profile and email
      const currentUserProfile = await this.userProfileRepository.findById(vendor.userProfileId);
      if (!currentUserProfile || currentUserProfile?.role !== 'clientsupplier') {
        return {
          status: false,
          message: "Current vendor user details not found"
        };
      }

      const currentUser = await this.userRepository.findById(currentUserProfile.userId);
      if (!currentUser) {
        return {
          status: false,
          message: "Current vendor email not found"
        };
      }

      // Validate if new email is same as current email
      if (currentUser.email === newEmail) {
        return {
          status: false,
          message: "New email is same as current email. No changes needed."
        };
      }

      // Check if the new email already exists in the system
      const existingUser = await this.userRepository.findOne({where: {email: newEmail}});

      if (existingUser) {
        // EMAIL EXISTS - Handle mapping case
        const existingUserProfile = await this.userProfileRepository.findOne({
          where: {userId: existingUser.id, role: role}
        });

        if (!existingUserProfile) {
          return {
            status: false,
            message: `Email exists but is not associated with a ${role} account`
          };
        }

        // Map the vendor to the existing userProfile
        await this.vendorCodeRepository.updateById(vendorId, {
          userProfileId: existingUserProfile.id,
          modified_on: new Date().toISOString(),
          modified_by: requesterId
        });

        // Send email notifications for mapping case
        const newPassword = this.generateRandomString();
        const passwordHash = await hash(newPassword, await genSalt());

        // Get admin/client details for email template
        const adminObj = await this.userProfileRepository.findById(existingUserProfile.clientId);

        // Update the existing user's password
        await this.userRepository
          .userCredentials(existingUser.id)
          .patch({password: passwordHash});

        // Update email sent count
        const counter = existingUserProfile?.emailSentCount ? existingUserProfile.emailSentCount + 1 : 1;
        await this.userProfileRepository.updateById(existingUserProfile.id, {emailSentCount: counter});

        // Get all vendor codes for this user profile for email content
        const allVendor = await this.vendorCodeRepository.find({where: {userProfileId: existingUserProfile.id}});
        const vendorDetails = this.getUniqueValidEmails(allVendor);

        // Prepare email content
        const subject = 'NAVIGOS Supplier Account Creation - TVSM';
        let body = `<p>Dear Supplier,</p><p>We are pleased to inform you that you have been successfully mapped as the Single Point of Contact (SPOC) for the respective vendor code on the Navigos platform.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p><p>  Upon initial login, we recommend to changing your password (use forgot password option on the login screen) for security purposes.</p> <p>Please note that ${newEmail} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p>`;

        // Send emails to additional contacts if they exist
        if (vendorDetails.length) {
          for (const element of vendorDetails) {
            for (const spocmail of element.emails) {
              const contactBody = `<p>Dear ${spocmail},</p><p>We are pleased to inform you that your Single Point of Contact (SPOC) has been changed on the Navigos platform.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${element.code}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p> <p>Please note that ${newEmail} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`;

              await this.sqsService.sendEmail(spocmail, subject, contactBody, []).catch((err) => {
                console.error('Error sending email to contact:', err);
              });
            }
          }
        }
        console.log('Email sent to SPOC:', newEmail);
        // Send main email to SPOC
        await this.sqsService.sendEmail(newEmail, subject, body, []).catch((err) => {
          console.error('Error sending email to SPOC:', err);
        });

        return {
          status: true,
          message: `Vendor successfully mapped to existing ${role} email: ${newEmail}`,
          data: {
            action: "mapped",
            vendorId: vendorId,
            oldEmail: currentUser.email,
            newEmail: newEmail,
            newUserProfileId: existingUserProfile.id,
            temporaryPassword: newPassword,
            mappedBy: requesterId,
            mappedOn: new Date().toISOString()
          }
        };
      } else {
        // EMAIL DOESN'T EXIST - Handle creation case
        if (!createIfNotExists) {
          return {
            status: false,
            message: "Email does not exist in the system. Set 'createIfNotExists' to true to create a new account."
          };
        }

        // Create new user account with the new email
        const newPassword = this.generateRandomString();
        const passwordHash = await hash(newPassword, await genSalt());

        // Get admin/client details for email template
        const adminObj = await this.userProfileRepository.findById(currentUserProfile.clientId);

        // Update the current user's email and password
        await this.userRepository
          .userCredentials(currentUser.id)
          .patch({password: passwordHash});

        // Remove reset key from database its no longer valid
        currentUser.verificationToken = '';

        // Update the user with new email
        await this.userRepository.updateById(currentUser.id, {...currentUser, email: newEmail});

        // Update email sent count
        const counter = currentUserProfile?.emailSentCount ? currentUserProfile.emailSentCount + 1 : 1;
        await this.userProfileRepository.updateById(currentUserProfile.id, {emailSentCount: counter});

        // Get all vendor codes for this user profile for email content
        const allVendor = await this.vendorCodeRepository.find({where: {userProfileId: currentUserProfile.id}});
        const vendorDetails = this.getUniqueValidEmails(allVendor);

        // Prepare email content
        const subject = 'NAVIGOS Supplier Account Creation - TVSM';
        let body = `<p>Dear Supplier,</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${allVendor?.map((x: any) => x?.code).filter(x => x).join(' / ')}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p><p>  Upon initial login, we recommend to changing your password (use forgot password option on the login screen) for security purposes.</p> <p>Please note that ${newEmail} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`;

        // Send emails to additional contacts if they exist
        if (vendorDetails.length) {
          for (const element of vendorDetails) {
            for (const spocmail of element.emails) {
              const contactBody = `<p>Dear ${spocmail},</p><p>We are pleased to inform you that your account has been successfully created on the Navigos platform as a Supplier.</p><p>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a>  </p>  <p>Please find below your login credentials: </p><p>Login Id: <strong>${element.code}</strong></p><p> Temporary Password: <strong>${newPassword} </strong></p> <p>Please note that ${newEmail} is the designated "Single Point of Contact (SPOC)" from your organization to access the TVSM's Navigos Platform.</p> <p>Should you encounter any difficulties or require assistance, <NAME_EMAIL> </p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`;

              await this.sqsService.sendEmail(spocmail, subject, contactBody, []).catch((err) => {
                console.error('Error sending email to contact:', err);
              });
            }
          }
        }

        // Send main email to new SPOC
        await this.sqsService.sendEmail(newEmail, subject, body, []).catch((err) => {
          console.error('Error sending email to SPOC:', err);
        });



        return {
          status: true,
          message: "New account created and vendor updated successfully",
          data: {
            action: "created",
            vendorId: vendorId,
            oldEmail: currentUser.email,
            newEmail: newEmail,
            userProfileId: currentUserProfile.id,
            temporaryPassword: newPassword,
            createdBy: requesterId,
            createdOn: new Date().toISOString()
          }
        };
      }

    } catch (error) {
      console.error('Error in mapVendorToExistingEmail:', error);
      return {
        status: false,
        message: "Failed to process vendor email change. Please contact support."
      };
    }
  }

  @post('/delete-complete-user-cognito')
  async deleteIndividualUserByUPIDInclCognito(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {
                type: 'number'
              },
              email: {
                type: 'string'
              },
              requestUser: {
                type: 'number'
              }
            },
            required: ['userProfileId', 'requestUser', 'email'],
          },
        },
      },
    })
    requestBody: {userProfileId: number, requestUser: number, email: string},
  ): Promise<any> {
    const {email} = requestBody;
    try {
      await cognitoTVSExternal.adminDeleteUser({
        UserPoolId: `${process.env.REACT_APP_TVS_AWS_EXTERNAL_USER_POOL_ID}`,
        Username: email,   // Cognito username or email (depends on your setup)
      }).promise()
      await this.userController.deleteIndividualUserByUPID(requestBody)
      return {status: true}
    } catch (err) {
      return {status: false, message: err}
    }





  }

  @post('/bulk-update-user-profile-id')
  @response(200, {
    description: 'Comprehensive bulk update for userProfileId, clientId, and tags across all repositories. Special handling: DpReport/DpReportNew/ChangeManagement/UserProfile for clientId updates, FormCollection/ConsolidateFormCollection for tags array updates, NewMetric/NewTopic for tag field updates.',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            updatedCounts: {type: 'object', description: 'Count of userProfileId updates by repository'},
            tagUpdates: {type: 'object', description: 'Count of tag/tags array updates by repository'},
            clientIdUpdates: {type: 'object', description: 'Count of clientId updates by repository'},
            errors: {type: 'array', description: 'Array of error messages if any operations failed'},
            summary: {type: 'object', description: 'Summary statistics of all updates performed'}
          }
        }
      }
    }
  })
  async bulkUpdateUserProfileId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              oldUserProfileId: {
                type: 'number',
                description: 'The current userProfileId to be replaced'
              },
              newUserProfileId: {
                type: 'number',
                description: 'The new userProfileId to replace with'
              },

            },
            required: ['oldUserProfileId', 'newUserProfileId'],
          },
        },
      },
    })
    requestBody: {oldUserProfileId: number, newUserProfileId: number},
  ): Promise<any> {
    const {oldUserProfileId, newUserProfileId} = requestBody;

    try {
      // Validate that both user profiles exist
      const oldUserProfile = await this.userProfileRepository.findById(oldUserProfileId).catch(() => null);
      const newUserProfile = await this.userProfileRepository.findById(newUserProfileId).catch(() => null);

      if (!oldUserProfile) {
        return {
          status: false,
          message: `Old UserProfile with ID ${oldUserProfileId} not found`,
          updatedCounts: {},
          tagUpdates: {},
          clientIdUpdates: {},
          errors: [],
          summary: {}
        };
      }

      if (!newUserProfile) {
        return {
          status: false,
          message: `New UserProfile with ID ${newUserProfileId} not found`,
          updatedCounts: {},
          tagUpdates: {},
          clientIdUpdates: {},
          errors: [],
          summary: {}
        };
      }



      const updatedCounts: any = {};
      const tagUpdates: any = {};
      const clientIdUpdates: any = {};
      const errors: string[] = [];

      // 1. USERPROFILEID UPDATES - Define repositories for userProfileId updates
      const userProfileIdUpdates = [
        {
          name: 'AssignDcfUser',
          repository: this.assignDcfUserRepository,
          updateField: 'userProfileId'
        },
        {
          name: 'AssignDcfEntityUser',
          repository: this.assignDcfEntityUserRepository,
          updateField: 'userProfileId'
        },
        {
          name: 'SupplierAssessmentAssignment',
          repository: this.supplierAssessmentAssignmentRepository,
          updateField: 'userProfileId'
        },
        {
          name: 'UserRoleAuthorization',
          repository: this.userRoleAuthorizationRepository,
          updateField: 'userProfileId'
        },
        {
          name: 'VendorCode',
          repository: this.vendorCodeRepository,
          updateField: 'userProfileId'
        }
      ];

      // 2. CLIENTID UPDATES - Define repositories for clientId updates
      const clientIdUpdateRepositories = [
        {
          name: 'DpReport',
          repository: this.dpReportRepository,
          updateField: 'clientId'
        },
        {
          name: 'DpReportNew',
          repository: this.dpReportNewRepository,
          updateField: 'clientId'
        },
        {
          name: 'ChangeManagement',
          repository: this.changeManagementRepository,
          updateField: 'clientId'
        },
        {
          name: 'VendorCode',
          repository: this.vendorCodeRepository,
          updateField: 'clientId'
        },
        {
          name: 'UserProfile',
          repository: this.userProfileRepository,
          updateField: 'clientId'
        }
      ];

      // PROCESS 1: UserProfileId Updates
      for (const repoUpdate of userProfileIdUpdates) {
        try {
          const updateData: any = {};
          updateData[repoUpdate.updateField] = newUserProfileId;

          const whereCondition: any = {};
          whereCondition[repoUpdate.updateField] = oldUserProfileId;

          const updateResult = await repoUpdate.repository.updateAll(
            updateData,
            whereCondition
          );

          updatedCounts[repoUpdate.name] = updateResult.count || 0;
        } catch (error) {
          const errorMessage = `Failed to update userProfileId in ${repoUpdate.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          updatedCounts[repoUpdate.name] = 0;
        }
      }

      // PROCESS 2: ClientId Updates - Replace oldUserProfileId with newUserProfileId in clientId fields
      for (const repoUpdate of clientIdUpdateRepositories) {
        try {
          const updateData: any = {};
          updateData[repoUpdate.updateField] = newUserProfileId;

          const whereCondition: any = {};
          whereCondition[repoUpdate.updateField] = oldUserProfileId;

          const updateResult = await repoUpdate.repository.updateAll(
            updateData,
            whereCondition
          );

          clientIdUpdates[repoUpdate.name] = updateResult.count || 0;
        } catch (error) {
          const errorMessage = `Failed to update clientId in ${repoUpdate.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          clientIdUpdates[repoUpdate.name] = 0;
        }
      }

      // PROCESS 3: Tag Updates - Replace oldUserProfileId with newUserProfileId in tag/tags arrays

      // 3.1 Update FormCollection tags arrays
      try {
        const formCollections = await this.formCollectionRepository.find({});
        let formCollectionUpdates = 0;

        for (const formCollection of formCollections) {
          let updated = false;
          const updateData: any = {};

          // Handle tags array - replace oldUserProfileId with newUserProfileId
          if (formCollection.tags && Array.isArray(formCollection.tags)) {
            const updatedTags = formCollection.tags.map((tag: any) => {
              // Convert to string for comparison
              const tagStr = tag
              const oldIdStr = oldUserProfileId
              const newIdStr = newUserProfileId

              return tagStr === oldIdStr ? newIdStr : tag;
            });

            if (JSON.stringify(updatedTags) !== JSON.stringify(formCollection.tags)) {
              updateData.tags = updatedTags;
              updated = true;
            }
          }

          if (updated) {
            await this.formCollectionRepository.updateById(formCollection.id, updateData);
            formCollectionUpdates++;
          }
        }

        tagUpdates['FormCollection'] = formCollectionUpdates;
      } catch (error) {
        const errorMessage = `Failed to update tags in FormCollection: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        tagUpdates['FormCollection'] = 0;
      }

      // 3.2 Update ConsolidateFormCollection tags arrays
      try {
        const consolidateFormCollections = await this.consolidateFormCollectionRepository.find({});
        let consolidateFormCollectionUpdates = 0;

        for (const consolidateFormCollection of consolidateFormCollections) {
          let updated = false;
          const updateData: any = {};

          // Handle tags array - replace oldUserProfileId with newUserProfileId
          if (consolidateFormCollection.tags && Array.isArray(consolidateFormCollection.tags)) {
            const updatedTags = consolidateFormCollection.tags.map((tag: any) => {
              // Convert to string for comparison
              const tagStr = tag
              const oldIdStr = oldUserProfileId
              const newIdStr = newUserProfileId

              return tagStr === oldIdStr ? newIdStr : tag;
            });

            if (JSON.stringify(updatedTags) !== JSON.stringify(consolidateFormCollection.tags)) {
              updateData.tags = updatedTags;
              updated = true;
            }
          }

          if (updated) {
            await this.consolidateFormCollectionRepository.updateById(consolidateFormCollection.id, updateData);
            consolidateFormCollectionUpdates++;
          }
        }

        tagUpdates['ConsolidateFormCollection'] = consolidateFormCollectionUpdates;
      } catch (error) {
        const errorMessage = `Failed to update tags in ConsolidateFormCollection: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        tagUpdates['ConsolidateFormCollection'] = 0;
      }

      // 3.3 Update NewMetric tag field
      try {
        const newMetrics = await this.newMetricRepository.find({});
        let newMetricUpdates = 0;

        for (const newMetric of newMetrics) {
          let updated = false;
          const updateData: any = {};

          // Handle tag field - replace oldUserProfileId with newUserProfileId if it matches
          if (newMetric.tag !== undefined && newMetric.tag !== null) {
            const tagStr = newMetric.tag
            const oldIdStr = oldUserProfileId

            if (tagStr === oldIdStr) {
              updateData.tag = newUserProfileId;
              updated = true;
            }
          }

          if (updated) {
            await this.newMetricRepository.updateById(newMetric.id, updateData);
            newMetricUpdates++;
          }
        }

        tagUpdates['NewMetric'] = newMetricUpdates;
      } catch (error) {
        const errorMessage = `Failed to update tag in NewMetric: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        tagUpdates['NewMetric'] = 0;
      }

      // 3.4 Update NewTopic tag field
      try {
        const newTopics = await this.newTopicRepository.find({});
        let newTopicUpdates = 0;

        for (const newTopic of newTopics) {
          let updated = false;
          const updateData: any = {};

          // Handle tag field - replace oldUserProfileId with newUserProfileId if it matches
          if (newTopic.tag !== undefined && newTopic.tag !== null) {
            const tagStr = newTopic.tag
            const oldIdStr = oldUserProfileId

            if (tagStr === oldIdStr) {
              updateData.tag = newUserProfileId;
              updated = true;
            }
          }

          if (updated) {
            await this.newTopicRepository.updateById(newTopic.id, updateData);
            newTopicUpdates++;
          }
        }

        tagUpdates['NewTopic'] = newTopicUpdates;
      } catch (error) {
        const errorMessage = `Failed to update tag in NewTopic: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        tagUpdates['NewTopic'] = 0;
      }

      // Calculate totals for summary
      const totalUserProfileUpdates = Object.values(updatedCounts).reduce((sum: number, count: any) => sum + (count || 0), 0);
      const totalClientIdUpdates = Object.values(clientIdUpdates).reduce((sum: number, count: any) => sum + (count || 0), 0);
      const totalTagUpdates = Object.values(tagUpdates).reduce((sum: number, count: any) => sum + (count || 0), 0);
      const totalUpdates = totalUserProfileUpdates + totalClientIdUpdates + totalTagUpdates;

      // Create comprehensive summary
      const summary = {
        userProfileIdUpdates: {
          repositories: Object.keys(updatedCounts).length,
          totalRecords: totalUserProfileUpdates
        },
        clientIdUpdates: {
          repositories: Object.keys(clientIdUpdates).length,
          totalRecords: totalClientIdUpdates
        },
        tagUpdates: {
          repositories: Object.keys(tagUpdates).length,
          totalRecords: totalTagUpdates
        },
        overall: {
          totalRepositories: Object.keys(updatedCounts).length + Object.keys(clientIdUpdates).length + Object.keys(tagUpdates).length,
          totalRecordsUpdated: totalUpdates,
          errorsCount: errors.length
        }
      };

      // Create detailed message
      let message = '';
      if (errors.length === 0) {
        const messageParts = [];
        if (totalUserProfileUpdates > 0) {
          messageParts.push(`userProfileId: ${totalUserProfileUpdates} records across ${Object.keys(updatedCounts).length} repositories`);
        }
        if (totalClientIdUpdates > 0) {
          messageParts.push(`clientId: ${totalClientIdUpdates} records across ${Object.keys(clientIdUpdates).length} repositories`);
        }
        if (totalTagUpdates > 0) {
          messageParts.push(`tags: ${totalTagUpdates} records across ${Object.keys(tagUpdates).length} repositories`);
        }
        message = `Successfully updated ${messageParts.join(', ')}. Total: ${totalUpdates} records updated.`;
      } else {
        message = `Completed with ${errors.length} errors. Updated ${totalUpdates} records total across ${summary.overall.totalRepositories} repositories.`;
      }

      return {
        status: errors.length === 0,
        message,
        updatedCounts,
        tagUpdates,
        clientIdUpdates,
        errors,
        summary
      };

    } catch (error) {
      return {
        status: false,
        message: `Failed to perform bulk updates: ${error instanceof Error ? error.message : 'Unknown error'}`,
        updatedCounts: {},
        tagUpdates: {},
        clientIdUpdates: {},
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        summary: {
          userProfileIdUpdates: {repositories: 0, totalRecords: 0},
          clientIdUpdates: {repositories: 0, totalRecords: 0},
          tagUpdates: {repositories: 0, totalRecords: 0},
          overall: {totalRepositories: 0, totalRecordsUpdated: 0, errorsCount: 1}
        }
      };
    }
  }

  isValidEmail(email: any) {
    if (!email?.trim()) return false; // Returns false for null, undefined, or empty string
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };
  getUniqueValidEmails(data: any) {
    const seenEmails = new Set(); // Global tracker for unique emails

    return data.map(({code, supplierEmail3, supplierEmail2}: any) => {
      const uniqueEmails = new Set(); // Local tracker to prevent duplicate emails within the same code

      [supplierEmail3, supplierEmail2].forEach((email: any) => {
        if (this.isValidEmail(email) && !seenEmails.has(email)) {
          uniqueEmails.add(email);
          seenEmails.add(email); // Track globally
        }
      });

      return {code, emails: [...uniqueEmails]};
    }).filter((entry: any) => entry.emails.length > 0); // Remove empty email lists
  }
  public async getUserProfileList(filter: Filter<UserProfile>): Promise<any[]> {
    const users = await this.userProfileRepository.find(filter);

    const returnUsers = await Promise.all(users.map(async (user) => {
      const userData = await this.userRepository.findById(user.userId);

      const newData = {...user, username: userData.username, email: userData.email};
      return newData;
    }));


    return returnUsers;
  }
  getSubmissionStatus(submission: any): string {
    return submission?.type
      ? ['Pending Submission', 'Under Review', 'Under Approval', 'Approved'][
      submission.type
      ] || 'Pending Submission'
      : 'Pending Submission';
  }
  async filterDerivedAndStandaloneWithIds(data: any[], overall: any[], id: number) {
    const childIds = new Set();

    const standaloneChildren: Record<number, number[]> = {};
    const dcfMap: Record<number, number[]> = {};
    const sapMap: Record<number, number[]> = {};
    const overallTag: Record<number, any[]> = {};
    const dcfList = await this.formCollectionRepository.find()
    // Helper: Get dcfIds from newDataPoint -> data1[0].datasource
    function getDcfIds(indicatorId: number): number[] {
      const indicator = overall.find(d => d.id === indicatorId);
      const ndp = indicator?.newDataPoints || [];
      const dcfId = ndp.flatMap((dp: any) => {
        // Extract datasource and dcf_ids from each data point
        const datasources = dp.data1?.map((d: any) => d.datasource).filter((ds: any) => ds != null) || [];
        const dcfIds = dp.data1?.flatMap((d: any) => {
          // Ensure dcf_ids is always an array
          const dcfIdsValue = d.dcf_ids;
          if (Array.isArray(dcfIdsValue)) {
            return dcfIdsValue;
          } else {
            return []; // If not array (string, number, etc.), treat as empty array
          }
        }).filter((dcfId: any) => dcfId != null) || [];

        // Combine both datasources and dcf_ids
        return [...datasources, ...dcfIds];
      })
        .filter((dcfId: any) => dcfId) // Remove null/undefined values
        .filter((v: any, i: any, a: any) => a.indexOf(v) === i) // Get unique values
        .filter((dcfId: any) => {
          // Check if dcfId exists in dcfList and matches tag criteria
          const dcfItem = dcfList.find((item: any) => item.id === dcfId);
          return dcfItem &&
            (!dcfItem.tags || !dcfItem.tags.length || dcfItem.tags.includes(id));
        })

      if (dcfId !== undefined && dcfId !== null) {
        return dcfId;
      }
      return [];
    }
    const allRecords = await this.reportNameTwoRepository.find();
    function getFrameworkeTagIds(
      indicatorId: number
    ): string[] {
      const indicator = overall.find((d) => d.id === indicatorId);
      if (!indicator || !indicator.data1 || !indicator.data1.length) return [];
      const d = indicator.data1[0];
      const mergeUnique = (a: any[], b: any[]) => Array.from(new Set([...(a || []), ...(b || [])]));
      const allIds = [
        ...mergeUnique(d.framework_mandatory || [], d.tags1 || []),
        ...mergeUnique(d.framework_mandatory_if_material || [], d.tags2 || []),
        ...mergeUnique(d.framework_good_to_have || [], d.tags3 || []),
        ...mergeUnique(d.framework_not_required || [], d.tags4 || [])
      ].filter(Boolean);

      const idToTitle = Object.fromEntries(allRecords.map(r => [r.id, r.title]));
      return [
        ...mergeUnique(d.framework_mandatory || [], d.tags1 || []).map((id: any) => idToTitle[id]).filter(Boolean),
        ...mergeUnique(d.framework_mandatory_if_material || [], d.tags2 || []).map((id: any) => idToTitle[id]).filter(Boolean),
        ...mergeUnique(d.framework_good_to_have || [], d.tags3 || []).map((id: any) => idToTitle[id]).filter(Boolean),
        ...mergeUnique(d.framework_not_required || [], d.tags4 || []).map((id: any) => idToTitle[id]).filter(Boolean)
      ];
    }
    function getSapIds(indicatorId: number): number[] {
      const indicator = overall.find(d => d.id === indicatorId);
      const ndp = indicator?.newDataPoints || [];
      const dcfId = ndp.flatMap((dp: any) => {
        // Extract datasource2 and dcf_ids from each data point
        const datasource2s = dp.data1?.flatMap((d: any) => {
          // Ensure datasource2 is always an array
          const datasource2Value = d.datasource2;
          if (Array.isArray(datasource2Value)) {
            return datasource2Value;
          } else {
            return []; // If not array (string, number, etc.), treat as empty array
          }
        }).filter((ds: any) => ds != null) || [];

        const dcfIds = dp.data1?.flatMap((d: any) => {
          // Ensure dcf_ids is always an array
          const dcfIdsValue = d.dcf_ids;
          if (Array.isArray(dcfIdsValue)) {
            return dcfIdsValue;
          } else {
            return []; // If not array (string, number, etc.), treat as empty array
          }
        }).filter((dcfId: any) => dcfId != null) || [];

        // Combine both datasource2s and dcf_ids
        return [...datasource2s, ...dcfIds];
      })
        .filter((v: any, i: any, a: any) => a.indexOf(v) === i).filter((x: any) => x)

      if (dcfId !== undefined && dcfId !== null) {
        return dcfId;
      }
      return [];
    }
    function collectStandaloneIds(itemId: number): number[] {
      const item = overall.find(d => d.id === itemId);
      const indicators = item?.data1?.[0]?.indicator || [];
      if (!Array.isArray(indicators)) return [];

      let standaloneIds = indicators.filter((id: any) => {
        const child = overall.find(d => d.id === id);
        return child?.data1?.[0]?.source === 1;
      });

      // Collect dcfIds for standalone indicators
      for (const sid of standaloneIds) {
        dcfMap[sid] = getDcfIds(sid);
        sapMap[sid] = getSapIds(sid);
        overallTag[sid] = getFrameworkeTagIds(sid);
      }

      // Recursively gather standalone children
      indicators.forEach((id: number) => {
        const child = overall.find(d => d.id === id);
        if (child?.data1?.[0]?.source === 0) {

          const nested = collectStandaloneIds(child.id);
          standaloneIds = [...standaloneIds, ...nested];
        }
      });

      return standaloneIds;
    }

    // Step 1: Gather standalone IDs under each derived
    data.forEach((item) => {
      const type = item?.data1?.[0]?.type;
      const source = item?.data1?.[0]?.source;

      if (type === 0 && source === 0) {

        const standaloneIds = collectStandaloneIds(item.id);
        if (standaloneIds.length) {
          standaloneChildren[item.id] = standaloneIds;
        }

        const indicators = item?.data1?.[0]?.indicator || [];
        indicators.forEach((id: number) => childIds.add(id));
      }
    });

    // Step 2: Map result and attach standalone_ids and dcfIds
    const filteredData = data.map(item => {
      const type = item?.data1?.[0]?.type;
      const source = item?.data1?.[0]?.source;
      const id = item.id;

      const isDerived = type === 0 && source === 0;
      const isStandalone = type === 0 && source === 1;

      if (isDerived && standaloneChildren[id]) {
        const standaloneIds = standaloneChildren[id];
        const dcfIds = standaloneIds.flatMap(sid => dcfMap[sid] || []);
        const sapIds = standaloneIds.flatMap(sid => sapMap[sid] || []);
        const overallTags = standaloneIds.flatMap(sid => overallTag[sid] || []);
        return {...item, standalone_ids: standaloneIds, dcfIds, sapIds, overallTags};
      }

      if (isStandalone) {
        const dcfIds = getDcfIds(id);
        const sapIds = getSapIds(id);

        const overallTags = getFrameworkeTagIds(id);
        return {...item, standalone_ids: [id], dcfIds, sapIds, overallTags};
      }

      if (!childIds.has(id)) {
        return item;
      }

      return null;
    }).filter(Boolean);

    return filteredData;
  }





  public async getUserByUPID(id: any) {
    if (!id) {return 'Not Found'}
    if (id) {
      const user = await this.filteredUP({where: {id}})
      if (user.length) {
        return user[0]?.information?.empname || 'NA'
      } else {
        return 'Not Found'
      }
    }
  }

  @get('send-dealer-reminder')
  async getDealerReminderSummary(): Promise<any> {
    const now = new Date();
    const currentDay = now.getDate();
    const lastDayOfMonth = DateTime.utc().endOf('month').day;
    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');
    const currentYear = now.getFullYear();
    const currentPeriod = `${currentMonth}-${currentYear}`;
    const email: any = []


    const submittedVendorIds = new Set<number>();
    ((await this.userProfileRepository.dealerChecklistSubmissions(289).find()) as DealerChecklistSubmission[]).forEach(assessment => {

      if (assessment?.reporting_period?.includes(currentPeriod)) {
        submittedVendorIds.add(assessment.vendorId);
      }
    });


    const dealerAssessmentAssignment = await this.userProfileRepository.dealerAssessmentAssignments(289).find({"include": ["dealer", "dealerAuditorChecklistSubmission", "vendor", "actions"]})
    const dealers = (await this.filteredUP({where: {role: 'clientdealer'}, include: ['vendorCodes']}))
    const notSubmittedVendors: {
      "Dealer Name": string,
      "email": string;
      "Zone": string;
      "Category": string;
      "Dealer Code": string;
      vendorId: number;
      type: number;
      dealerAction: any;
      msiScore: any;
      submitted: any;
      latestSelfAssessmentMonth: any;
      headMailIds: any
    }[] = [];
    const zonalOfficeList = [{name: "Central", value: 1}, {name: "East", value: 2}, {name: "North", value: 3}, {name: "South", value: 9}, {name: "South1", value: 4}, {name: "South2", value: 5}, {name: "West", value: 8}, {name: "West1", value: 6}, {name: "West2", value: 7}, {name: "TN", value: 10}, {name: "North1", value: 11}, {name: "North2", value: 12}]
    const dealerType = [{name: 'Authorized Main Dealer', value: 1}, {name: 'Authorized Dealer', value: 2}, {name: 'Authorized Parts Stockist (APS)', value: 3}, {name: 'Area Office', value: 4}]
    let dealerChecklistSubmission: any = [], submitted: any = null, msiScore = 'NA', latestSelfAssessmentMonth: string = 'NA';
    for (const dealer of dealers) {

      if (dealer.vendorCodes) {
        for (const vendorCode of dealer.vendorCodes) {

          const vendorId = vendorCode?.id || null;
          const triggered = vendorCode.assessmentStartMonth && DateTime.now().startOf('month') >= DateTime.fromISO(vendorCode.assessmentStartMonth, {zone: 'utc'}).startOf('month')

          if (vendorId && !submittedVendorIds.has(vendorId) && dealer.email) {

            const dealerAction = dealerAssessmentAssignment.filter((x: any) => x.vendorId === vendorCode.id && Array.isArray(x?.actions) && x?.actions?.length > 0).flatMap(x => x.actions.filter((y: any) => !['Completed'].includes(y?.status)))

            if (triggered || dealerAction.length) {
              if (false) {
                dealerChecklistSubmission = await this.userProfileRepository.dealerChecklistSubmissions(289).find({where: {vendorId: vendorId}})
                submitted = dealerChecklistSubmission.find((x: any) => x.reporting_period?.[0] === DateTime.utc().toFormat('MM-yyyy') && x.type === 1)
                if (submitted) {
                  msiScore = JSON.parse(submitted?.score || '{}').overallScore || 'NA'
                  latestSelfAssessmentMonth = DateTime.utc().toFormat('LL-yyyy').toString()

                } else {
                  const lastSubmissions = dealerChecklistSubmission.filter((x: any) => x.type === 1)?.sort((a: any, b: any) => {
                    const dateA: any = moment(a.reporting_period?.[0], 'MM-YYYY');
                    const dateB: any = moment(b.reporting_period?.[0], 'MM-YYYY');
                    return dateB - dateA;
                  }) || []
                  if (lastSubmissions.length > 0) {
                    msiScore = JSON.parse(lastSubmissions[0]?.score || '{}')?.overallScore || 'NA'
                    latestSelfAssessmentMonth = DateTime.fromFormat(lastSubmissions[0]?.reporting_period?.[0] || null, 'MM-yyyy').toFormat('LL-yyyy')


                  }
                }
              }

              notSubmittedVendors.push({
                submitted: submitted ? true : false,
                msiScore,
                latestSelfAssessmentMonth,
                type: (triggered && dealerAction.length) ? 3 : dealerAction.length ? 2 : 1,
                email: dealer?.email || '',
                "Dealer Code": vendorCode?.code || 'NA',
                "Dealer Name": vendorCode?.dealerName || '',
                vendorId: vendorId,
                dealerAction,
                "Zone": zonalOfficeList.find(x => x.value === vendorCode.dealerZone)?.name || '',
                "Category": dealerType.find(x => x.value === vendorCode.dealerCategory)?.name || '',
                headMailIds: this.extractDealerHeadValidEmails(vendorCode, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])
              });


              if (currentDay === 1) {
                const subject = `Submit Monthly MSI Self-Assessment & Pending Action Points - ${vendorCode.dealerName} (${vendorCode.code})`
                let body = `<p>Dear ${vendorCode.dealerName}</p>
                <p style='margin: 5px 0px;'>Hope you're doing well.<p>
               ${triggered === true ? `<p style='margin: 5px 0px;'>As part of our <strong>"My Sustainability Index"</strong> program, which aims to enhance sustainable practices across our value chain, we kindly request you to complete the <strong>"Self-Assessment"</strong> form.</p>
 <p style='margin: 5px 0px;'>Your monthly Self-Assessment is now live on the Eisqr - ESG Platform ("Acuizen Application"). We request you to complete the assessment by the <strong>15th of this month</strong></p>
 <p ><strong>What You Need to Do:</strong></p>
 <ol>
 <li>
 <strong>Log in</strong> to the AcuiZen mobile application using your existing credentials:
 <ul>
  <li>Download for iOS</li>
  <li>Download for Android </li>
  <p style="font-style:italic;margin: 5px 0px;" >(Alternatively, search “AcuiZen” in the App Store or Google Play Store)</p>
 </ul>
  </li>
  <li>After installation, open the app and enter <strong>tvsdealer</strong> as the Enterprise ID.Next, enter your Branch ID and Password to log in. </li>
  <li>Once logged in, navigate to the Self-Assessment section and submit your responses before the due date</li>
   <li><strong>Complete</strong> all sections of the form with accurate and updated information, including:</li>
  </ol>
  <p style='margin: 5px 0px;'>The self-assessment form includes questions on your sustainability policies, initiatives, etc. Please upload supporting evidence (e.g., certifications, licenses reports) within the form.</p>` : ''
                  }
  ${dealerAction.length !== 0 ?
                    ` <p style='margin: 5px 0px;'>This is also a gentle reminder regarding the pending action plans, as of today.</p>  <p style='margin: 5px 0px;'> According to our records, the following action items remain unresolved. We kindly request you to review these action points and provide updated status along with relevant supporting evidence.</p>
    ${this.helper.generateHtmlTable(dealerAction?.map((x: any, index) => ({"Sr No": index + 1, "Action Assinged On": DateTime.fromISO(x.createdDate, {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy'), "Assigned Action": x.actionToBeTaken, "Due Date": DateTime.fromISO(x.dueDate, {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy')})))}
    <p style='margin: 5px 0px;'>To proceed, please click on <a href="https://tvsmotor-partner.eisqr.com" >EiSqr – TVS Partner Platform </a> to log in and complete the assigned action(s).</p>
    `: ''}

  <p style='margin: 5px 0px;'>If you need any support or clarification, feel free to contact <NAME_EMAIL>, and <NAME_EMAIL>.
Your participation is vital to the success of the "My Sustainability Index" program, and we greatly appreciate your cooperation in helping us build a sustainable future together.</p>

  <p style='margin: 5px 0px;'>Thank you once again for your commitment to this shared journey.</p>
  <p style='margin: 5px 0px;'>Warm regards,</p>
  <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
   <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
 `
                email.push({email: dealer.email, subject, body})
              } else if (currentDay === 15) {

                if ((triggered && !submitted) || dealerAction.length) {
                  const subject = ((triggered && !submitted && dealerAction.length) ? 'Action Required: Overdue Monthly Self-Assessment & Pending Audit Action Items on Eisqr – ESG Platform' : (triggered && !submitted) ? 'Action Required: Overdue Monthly Self-Assessment on Eisqr – ESG Platform' : 'Action Required:Pending Audit Action Items on Eisqr – ESG Platform') + `  ${vendorCode.dealerName} (${vendorCode.code})`
                  let body = `<p>Dear ${vendorCode.dealerName}</p>
                  <p style='margin: 5px 0px;'>Hope you're doing well.<p>
                 ${triggered === true && !submitted ? `<p style='margin: 5px 0px;'>This is a reminder to complete your monthly Self-Assessment on the <strong> Eisqr – ESG Platform (“Acuizen” Application)</strong>, if not already done. The submission deadline of the 15th of this month has now passed.</p>
   <p style='margin: 5px 0px;'>Your input plays a key role in promoting transparency, ensuring compliance, and strengthening ESG performance across our value chain. We kindly request you to submit your Self-Assessment at the earliest without further delay.</p>
   <p >To complete the assessment:</p>
   <ol>
   <li>
   <strong>Log in</strong> to the AcuiZen mobile application using your existing credentials:
   <ul>
    <li>Download for iOS</li>
    <li>Download for Android </li>
    <p style="font-style:italic; margin: 5px 0px;" >(Alternatively, search “AcuiZen” in the App Store or Google Play Store)</p>
   </ul>
    </li>
    <li>After installation, open the app and enter <strong>tvsdealer</strong> as the Enterprise ID.Next, enter your Branch ID and Password to log in. </li>
    <li>Once logged in, navigate to the Self-Assessment section and submit your responses along with supporting documents, as required.</li>
     <li><strong>Complete</strong> all sections of the form with accurate and updated information, including:</li>
    </ol>
    <p style='margin: 5px 0px;'>The self-assessment form includes questions on your sustainability policies, initiatives, etc. Please upload supporting evidence (e.g., certifications, licenses reports) within the form.</p>` : ''
                    }
    ${dealerAction.length !== 0 ?
                      ` <p style='margin: 5px 0px;'>This is gentle reminder regarding the pending action plans, as of today.</p>  <p style='margin: 5px 0px;'> According to our records, the following action items remain unresolved. We kindly request you to review these action points and provide the latest status along with relevant supporting evidence:</p>
      ${this.helper.generateHtmlTable(dealerAction?.map((x: any, index) => ({"Sr No": index + 1, "Action Assinged On": DateTime.fromISO(x.createdDate, {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy'), "Assigned Action": x.actionToBeTaken, "Due Date": DateTime.fromISO(x.dueDate, {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy')})))}
      <p style='margin: 5px 0px;'>To proceed, please click on <a href="https://tvsmotor-partner.eisqr.com" >EiSqr – TVS Partner Platform </a> to log in and complete the assigned action(s).</p>
      `: ''}

    <p style='margin: 5px 0px;'>For any assistance, feel free to contact <NAME_EMAIL>, copying <EMAIL></p>
    <p style='margin: 5px 0px;'>Thank you for your immediate attention to these matters. Your continued cooperation is greatly appreciated as we work together towards a more sustainable future.</p>
    <p style='margin: 5px 0px;'>Warm regards,</p>
    <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
     <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
   `
                  email.push({email: dealer.email, subject, body})

                }

              } else if (currentDay === 25) {
                const dealerChecklistSubmission = await this.userProfileRepository.dealerChecklistSubmissions(289).find({where: {vendorId: vendorId}})
                const submitted = dealerChecklistSubmission.find(x => x.reporting_period?.[0] === DateTime.utc().toFormat('MM-yyyy') && x.type === 1)
                if ((triggered && !submitted) || dealerAction.length) {
                  const subject = ((triggered && !submitted && dealerAction.length) ? 'Action Required: Overdue Monthly Self-Assessment & Pending Audit Action Items on Eisqr – ESG Platform' : (triggered && !submitted) ? 'Action Required: Overdue Monthly Self-Assessment on Eisqr – ESG Platform' : 'Action Required:Pending Audit Action Items on Eisqr – ESG Platform') + `  ${vendorCode.dealerName} (${vendorCode.code})`
                  // const cc = this.extractDealerHeadValidEmails(vendorCode, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])
                  let body = `<p>Dear ${vendorCode.dealerName}</p>
                  <p style='margin: 5px 0px;'>Hope you're doing well.<p>
                 ${triggered === true && !submitted ? `<p style='margin: 5px 0px;'>This is a reminder to complete your monthly Self-Assessment on the <strong> Eisqr – ESG Platform (“Acuizen” Application)</strong>, if not already done. The submission deadline, which was scheduled for the 15th of this month, has now passed.</p>
   <p style='margin: 5px 0px;'>Your input plays a key role in promoting transparency, ensuring compliance, and strengthening ESG performance across our value chain. We kindly request you to submit your Self-Assessment at the earliest without further delay.</p>
   <p >To complete the assessment:</p>
   <ol>
   <li>
   <strong>Log in</strong> to the AcuiZen mobile application using your existing credentials:
   <ul>
    <li>Download for iOS</li>
    <li>Download for Android </li>
    <p style="font-style:italic; margin: 5px 0px;" >(Alternatively, search “AcuiZen” in the App Store or Google Play Store)</p>
   </ul>
    </li>
    <li>After installation, open the app and enter <strong>tvsdealer</strong> as the Enterprise ID.Next, enter your Branch ID and Password to log in. </li>
    <li>Once logged in, navigate to the Self-Assessment section and submit your responses along with supporting documents, as required.</li>
     <li><strong>Complete</strong> all sections of the form with accurate and updated information, including:</li>
    </ol>
    <p style='margin: 5px 0px;'>The self-assessment form includes questions on your sustainability policies, initiatives, etc. Please upload supporting evidence (e.g., certifications, licenses reports) within the form.</p>` : ''
                    }
    ${dealerAction.length !== 0 ?
                      ` <p style='margin: 5px 0px;'>This is gentle reminder regarding the pending action plans, as of today.</p>  <p style='margin: 5px 0px;'> According to our records, the following action items remain unresolved. We kindly request you to review these action points and provide the latest status along with relevant supporting evidence:</p>
      ${this.helper.generateHtmlTable(dealerAction?.map((x: any, index) => ({"Sr No": index + 1, "Action Assinged On": DateTime.fromISO(x.createdDate, {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy'), "Assigned Action": x.actionToBeTaken, "Due Date": DateTime.fromISO(x.dueDate, {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy')})))}
      <p style='margin: 5px 0px;'>To proceed, please click on <a href="https://tvsmotor-partner.eisqr.com" >EiSqr – TVS Partner Platform </a> to log in and complete the assigned action(s).</p>
      `: ''}

    <p style='margin: 5px 0px;'>For any assistance, feel free to contact <NAME_EMAIL>, copying <EMAIL></p>
    <p style='margin: 5px 0px;'>Thank you for your immediate attention to these matters. Your continued cooperation is greatly appreciated as we work together towards a more sustainable future.</p>
    <p style='margin: 5px 0px;'>Warm regards,</p>
    <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
     <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
   `
                  email.push({email: dealer.email, subject, body})

                }

              }

            }
          }
        }
      }
    }

    if (currentDay === 25) {
      if (notSubmittedVendors.length > 0) {

        const areaManagerMailIds = (await this.vendorCodeRepository.find({
          where: {
            dealerName: {
              neq: null
            },
          },
        })).map((x) => this.extractDealerHeadValidEmails(x, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])).flatMap(x => x).filter(x => x)
        const roles = await this.userRoleAuthorizationRepository.execute(

          `SELECT * FROM UserRoleAuthorization
      WHERE roles IS NOT NULL
        AND JSON_LENGTH(roles) > 0
        AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify([13])]
        )

        const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
        const headUserData = await this.filteredUP({where: {id: {inq: headUserIds}}})
        const sectionAdminMailIds = headUserData.map((x: any) => x.email).filter((x: any) => x)
        const cc = Array.from(new Set(areaManagerMailIds))
        const subject = 'Escalation: Dealers with Pending Self-Assessments for ' + DateTime.utc().toFormat('LLL-yyyy')

        let body = `<p>Dear Team</p>
      <p style='margin: 5px 0px;'>Hope you're doing well.<p>
  <p style='margin: 5px 0px;'>This is to bring to your attention that the following dealers have not yet completed their <strong>Monthly Self-Assessment</strong> on the <strong>Eisqr – ESG Platform (Acuizen)</strong>, despite the due date of 15th ${DateTime.utc().toFormat('LLL-yyyy')} and an initial reminder.</p>
<p>Please find below the list of dealers with pending submissions.</p>
${this.helper.generateHtmlTable(notSubmittedVendors?.map(({type, email, vendorId, dealerAction, headMailIds, latestSelfAssessmentMonth, submitted, msiScore, ...rest}) => ({...rest})).sort((a, b) => a.Zone.localeCompare(b.Zone)))}


<p style='margin: 5px 0px;'>We kindly request your support in <strong>escalating this matter</strong> to the respective <strong>Regional/Area Managers and dealership teams </strong> and urge the concerned dealers to complete their assessments at the earliest. Timely completion is essential to maintain consistency and compliance across the value-chain.</p>
<p style='margin: 5px 0px;'>Thank you for your cooperation.</p>
<p style='margin: 5px 0px;'>Warm regards,</p>
<p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
 <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
`


        email.push({email: [...sectionAdminMailIds], body, subject, cc})

        // Example: Send the full list as an <NAME_EMAIL>
        // await this.emailQueue.sendSummaryReport('<EMAIL>', summary);
      }
    } else if (false) {
      const groupedAreaManagerMailId = this.groupByHeadMailIds(notSubmittedVendors) || {}

      Object.entries(groupedAreaManagerMailId).forEach(async ([key, value]) => {
        if (key !== 'unknown') {
          const subject = 'Monthly Summary: Dealer Self-Assessment Status – ' + DateTime.utc().toFormat('LLL-yyyy')

          let body = `<p>Dear ${key}</p>
        <p style='margin: 5px 0px;'>Hope you're doing well.<p>
    <p style='margin: 5px 0px;'>As we close out the month, please find below the consolidated status of monthly Self-Assessments across the dealer network for <strong>${DateTime.utc().toFormat('LLL-yyyy')}</strong>. The summary includes both <strong>completed and pending assessments</strong>, along with the <strong>latest submission month</strong> and <strong>MSI Self-Assessment Scores</strong>.</p>
    <p style='margin: 5px 0px;'><strong>Completed Self-Assessments</strong>.<p>
    ${this.helper.generateHtmlTable(value?.filter(x => x.submitted).map((item) => ({"Dealer Code": item['Dealer Code'], "Dealer Name": item['Dealer Name'], "Category": item['Category'], "Zone": item['Zone'], "MSI Self-Assessment Score": item['msiScore']})))}
    <p style='margin: 5px 0px;'><strong>Pending Self-Assessments</strong>.<p>
   ${this.helper.generateHtmlTable(value?.filter(x => !x.submitted).map((item) => ({"Dealer Code": item['Dealer Code'], "Dealer Name": item['Dealer Name'], "Category": item['Category'], "Zone": item['Zone'], "Latest Self-Assessment Month": item['latestSelfAssessmentMonth'], "Latest MSI Score": item['msiScore']})))}

  <p style='margin: 5px 0px;'>For any queries or further assistance, please reach <NAME_EMAIL>.</p>
  <p style='margin: 5px 0px;'>Thank you for your attention to this summary and for your continued collaboration.</p>
  <p style='margin: 5px 0px;'>Warm regards,</p>
  <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
  `
          email.push({email: key, body, subject, cc: ['<EMAIL>', '<EMAIL>', '<EMAIL>']})
        }
      })

    }
    return [{
      id: 289, email
    }]

  }
  @get('send-dealer-calibration-notification-reminder')
  async sendCalibrationNotificationReminderSummary(): Promise<any> {

    const utcStart = DateTime.utc().plus({days: 1}).startOf('day'); // Start of tomorrow (UTC)
    const utcEnd = utcStart.endOf('day'); // End of tomorrow (UTC)
    const email: any = []
    // Fetch the calibration assignments from the repository
    const calibrationAssignment = await this.userProfileRepository.dealerAssessmentAssignments(289).find();

    // Filter assignments based on the auditStartDate
    const filteredAssignment = calibrationAssignment.filter((item: any) => {
      // Ensure the auditStartDate is in UTC and then add 1 day
      const auditDate = DateTime.fromISO(item.auditStartDate ? item.auditStartDate : '', {zone: 'utc'}).plus({days: 1});

      // Compare auditDate to ensure it's within the range of tomorrow
      return auditDate >= utcStart && auditDate <= utcEnd;
    });
    const roles = await this.userRoleAuthorizationRepository.execute(

      `SELECT * FROM UserRoleAuthorization
      WHERE roles IS NOT NULL
        AND JSON_LENGTH(roles) > 0
        AND JSON_CONTAINS(roles, ?, '$')`,
      [JSON.stringify([13])]
    )

    const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
    const headUserData = await this.filteredUP({where: {id: {inq: headUserIds}}})
    const sectionAdminMailIds = headUserData.map((x: any) => x.email).filter((x: any) => x)

    for (const assignment of filteredAssignment) {
      const vendorData = await this.vendorCodeRepository.findById(assignment.vendorId)
      const vendorSpoc = await this.filteredUP({where: {id: vendorData.userProfileId}})
      if (vendorData && vendorSpoc && vendorSpoc[0]?.email) {

        const auditorData = await this.filteredUP({where: {id: {inq: assignment?.assessors || []}}})
        if (auditorData.length) {
          const headMailids = this.extractDealerHeadValidEmails(vendorData, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])

          const subject = "Reminder: MSI Calibration Audit Scheduled for Tomorrow – " + vendorData.dealerName
          const body = `<p>Dear Auditor(s) and ${vendorData.dealerName} </p>
    <p  style="margin: 5px 0px;" >Greetings from TVS Motors </p>
      <p  style="margin: 5px 0px;" >This is a gentle reminder that the <strong>MSI Calibration</strong> for the following dealership is scheduled for <strong>tomorrow</strong>. Please find the Calibration details below: </p>
        <p   ><strong> Dealer Details :</strong> </p>
        <ul>
  <li>Dealer Name :<strong>${vendorData.dealerName}</strong> </li>
  <li>Dealer Code :<strong>${vendorData.code}</strong> </li>
  <li>Dealer Location :<strong>${vendorData.dealerLocation}</strong> </li>
        </ul>
           <p   ><strong> Calibration Schedule :</strong> </p>
        <ul>
  <li>Scheduled Date :<strong>${DateTime.fromISO(assignment?.auditStartDate ? assignment.auditStartDate : '', {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy')}</strong> </li>
  ${auditorData?.map((item: any, index: number) => {
            return (
              `<li>Auditor Name  :<strong>${item?.information?.empname || 'NA'}</strong> </li>
  <li>Auditor Email Id :<strong>${item?.email || 'NA'}</strong> </li>`

            )
          })}
        </ul>
          <p  style="margin: 5px 0px;" >We kindly request the dealership team (Sales Manager, Works Manager, HR & Finance) to ensure their presence and make the necessary arrangements for a smooth and effective Calibration. </p>
   <p  style="margin: 5px 0px;" >We also request the assigned auditor to be fully prepared with all required tools and materials to conduct the Calibration as scheduled.</p>
    <p  style="margin: 5px 0px;" >For any queries or assistance, feel free to reach out to us at <strong><EMAIL></strong> and copy <strong><EMAIL>.</strong></p>
     <p  style="margin: 5px 0px;" >Thank you for your cooperation.</p>
     <p style='margin: 5px 0px;'>Best regards,,</p>
  <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
      <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
    `
          email.push({email: [...auditorData.map((x: any) => x.email), vendorSpoc[0]?.email], subject, body, cc: [...headMailids, ...sectionAdminMailIds]})
        }
      }
    }
    return [{
      id: 289, email
    }]
  }
  @get('send-spoc-action-approval-reminder')
  async sendActionApprovalNotificationReminderSummary(): Promise<any> {
    const now = new Date();
    const currentDay = now.getDate();
    const email: any = []
    if (currentDay === 15 || currentDay === 27) {
      const dealerAssessmentAssignment = await this.userProfileRepository.dealerAssessmentAssignments(289).find({"include": ["dealer", "dealerAuditorChecklistSubmission", "vendor", "actions"]})
      const zonalOfficeList = [{name: "Central", value: 1}, {name: "East", value: 2}, {name: "North", value: 3}, {name: "South", value: 9}, {name: "South1", value: 4}, {name: "South2", value: 5}, {name: "West", value: 8}, {name: "West1", value: 6}, {name: "West2", value: 7}]

      const dealerType = [{name: 'Authorized Main Dealer', value: 1}, {name: 'Authorized Dealer', value: 2}, {name: 'Authorized Parts Stockist (APS)', value: 3}, {name: 'Area Office', value: 4}]


      const dealers = (await this.filteredUP({where: {role: 'clientdealer'}, include: ['vendorCodes']}))

      for (const dealer of dealers) {

        if (dealer.vendorCodes) {
          for (const vendorCode of dealer.vendorCodes) {
            const dealerAction = dealerAssessmentAssignment.filter((x: any) => x.vendorId === vendorCode.id && Array.isArray(x?.actions) && x?.actions?.length > 0).flatMap(x => x.actions.filter((y: any) => y.status !== 'Completed' && ['Checklist Submission Review'].includes(y?.actionType)))
            if (dealerAction.length) {
              const subject = 'Request for Review and Approval – MSI Calibration Action Plan Tracker'
              const body = `
        <p>Dear Madhu</p>
                   <p style='margin: 5px 0px;'>Please find attached the <strong>Action Plan Tracker</strong> for your review and approval. The below listed actions have been submitted by the dealer(s) as part of the closure process following the MSI Calibration. All relevant documents are also available on the Eisqr platform.<p>
         ${this.helper.generateHtmlTable(dealerAction?.map((x: any, index) => ({"Sr No": index + 1, "Dealer Id": vendorCode.code, "Dealer Name": vendorCode.dealerName, "Zone": zonalOfficeList.find(x => x.value === vendorCode.dealerZone)?.name || '', "Category": dealerType.find(x => x.value === vendorCode.dealerCategory)?.name || '', "Assigned Action": x.actionToBeTaken, "Action Tacken": x.actionTaken, "Due Date": DateTime.fromISO(x.dueDate ? x.dueDate : '', {zone: 'utc'}).plus({'day': 1}).toFormat('dd-MM-yyyy')})).sort((a, b) => a.Zone.localeCompare(b.Zone)))}
         <p style='margin: 5px 0px;'>Kindly review the submitted action details and supporting documents, and mark each as <strong>Approved / Not Approved</strong> based on compliance with Calibration observations.</p>
          <p>Please log in to the <a href="https://tvsmotor.eisqr.com"> EiSqr – ESG Platform </a> to review and approve the action.</p>

  <p style='margin: 5px 0px;'>For any queries or further assistance, please reach <NAME_EMAIL>.</p>
  <p style='margin: 5px 0px;'>Best regards,</p>
  <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
                   `
              email.push({email: '<EMAIL>', subject, body, cc: '<EMAIL>'})

            }
          }
        }
      }
    }

    return [{
      id: 289, email
    }]
  }
  @get('supplier-self-assessment-trigger-mail')
  async supplierSelfAssessmentTriggerMail(): Promise<any> {

    const utcStart = DateTime.utc().plus({days: 1}).startOf('day'); // Start of tomorrow (UTC)
    const utcEnd = utcStart.endOf('day'); // End of tomorrow (UTC)
    const email: any = []
    // Fetch the calibration assignments from the repository
    const roleAgainsCategory = {
      1: 25,
      2: 26,
      3: 27,
      4: 28,
      5: 29,
      6: 30,
      7: 31,
      8: 32,
      9: 33,
      10: 34,
      11: 35
    }

    const supplierSelfAssignment = await this.userProfileRepository.supplierAssessmentAssignments(289).find(
      {

        "include": [
          {
            "relation": "supplierAssignmentSubmission", scope: {fields: {type: true, supplierMSIScore: true, submitted_on: true, id: true}}
          },


          {
            "relation": "auditorAssignmentSubmission", scope: {fields: {type: true, approved_on: true, approverComments: true, rejected_on: true, auditorMSIScore: true, submitted_on: true, modified_on: true, id: true}}
          },
          {
            "relation": "vendor"
          }
        ]

      }
    );


    const today = DateTime.utc().startOf('day');



    for (const assignment of supplierSelfAssignment) {
      const assessmentEndDate = DateTime.fromISO(assignment.assessmentEndDate ?? '', {zone: 'utc'}).plus({'day': 1});
      const diffInDays = assessmentEndDate.diff(today, 'days').days;

      if (!assignment.assessmentEndDate || isNaN(diffInDays)) continue;

      // Skip if supplierAssignmentSubmission.type === 1
      const supplierSubmission = assignment.supplierAssignmentSubmission;
      if (supplierSubmission && supplierSubmission.type === 1) {
        continue; // Skip sending reminder
      }

      let reminderType: 'reminder1' | 'reminder2' | 'reminder3' | null = null;
      let cc: any = []


      const vendorData = await this.vendorCodeRepository.findById(assignment.vendorId);
      const vendorSpoc = await this.filteredUP({where: {id: vendorData.userProfileId}});
      const supplierOtherSpoc = this.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)

      if (vendorData && vendorSpoc && vendorSpoc[0]?.email) {
        let subject = '';
        let body = '';
        const role_id = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
        const roles = await this.userRoleAuthorizationRepository.execute(

          `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify([role_id])]
        )
        const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
        const headUserData = await this.filteredUP({where: {id: {inq: headUserIds}}})
        const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
        if (diffInDays === 7) {
          reminderType = 'reminder1';
        } else if (diffInDays === 5) {
          reminderType = 'reminder2';
          cc = [...headSpocMailId, '<EMAIL>', '<EMAIL>']
        } else if (diffInDays === 3) {
          reminderType = 'reminder3';
          cc = [...headSpocMailId, '<EMAIL>', '<EMAIL>']
        } else {
          continue;
        }
        if (reminderType === 'reminder1') {
          subject = ` Action Required: Overdue Monthly Self-Assessment & Pending Audit Action Items on Navigos – ESG Platform -  – ${vendorData.supplierName}`;
          body = `<p  style='margin: 5px 0px;'>Dear ${vendorData.supplierName},</p>
<p  style='margin: 5px 0px;'>Hope you are doing well.</p>


<p style='margin: 5px 0px;'>This is a gentle reminder to complete your Self-Assessment on the Navigos Sustainability Platform, if not already done. Please note that the deadline for submission is <strong>${assessmentEndDate.toFormat('dd-MM-yyyy')}</strong>, which is 7 days away.</p>

<p  style='margin: 5px 0px;'>Your timely input is crucial in promoting transparency, ensuring compliance, and enhancing ESG performance across our value chain. We encourage you to complete the self-assessment at your earliest convenience to avoid last-minute delays.</p>
   <p style='margin: 5px 0px;'>please log in to the  <a href="https://tvsmotor-supplier.eisqr.com" target="_blank">EiSqr – ESG Platform</a> to complete the self-assessment.</p>
<p  style='margin: 5px 0px;'>
For any support or queries, please reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a>,
copying <a href="mailto:<EMAIL>"><EMAIL></a>.We sincerely appreciate your cooperation and commitment to sustainability.</p>
<p>Warm regards,<br/>
<strong>TVS Motor Company Limited</strong></p>

<p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>`;


        }

        if (reminderType === 'reminder2') {
          subject = ` Action Required: Overdue Monthly Self-Assessment & Pending Audit Action Items on Navigos – ESG Platform – ${vendorData.supplierName}`;

          body = `<p style='margin: 5px 0px;'>Dear ${vendorData.supplierName},</p>
        <p style='margin: 5px 0px;'>Hope you are doing well.</p>

<p style='margin: 5px 0px;'>This is a reminder to complete your Self-Assessment on the Navigos Sustainability Platform, if not already done. Please note that the deadline for submission is <strong>${assessmentEndDate.toFormat('dd-MM-yyyy')}</strong>), which is 5 days away.</p>

<p  style='margin: 5px 0px;'>Your timely input is crucial in promoting transparency, ensuring compliance, and enhancing ESG performance across our value chain. We encourage you to complete the self-assessment at your earliest convenience to avoid last-minute delays.</p>
   <p style='margin: 5px 0px;'>please log in to the  <a href="https://tvsmotor-supplier.eisqr.com" target="_blank">EiSqr – ESG Platform</a> to complete the self-assessment.</p>
<p  style='margin: 5px 0px;'>
For any support or queries, please reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a>,
copying <a href="mailto:<EMAIL>"><EMAIL></a>.We sincerely appreciate your cooperation and commitment to sustainability.</p>
<p>Warm regards,<br/>
<strong>TVS Motor Company Limited</strong></p>
<p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p> `;
        }

        if (reminderType === 'reminder3') {
          subject = ` Action Required: Overdue Monthly Self-Assessment & Pending Audit Action Items on Navigos – ESG Platform – ${vendorData.supplierName}`;
          body = `<p style='margin: 5px 0px;'>Dear ${vendorData.supplierName},</p>
   <p  style='margin: 5px 0px;'>Hope you are doing well.</p>

<p  style='margin: 5px 0px;'>This is a reminder to complete your Self-Assessment on the Navigos Sustainability Platform, if not already done. Please note that the deadline for submission is <strong>${assessmentEndDate.toFormat('dd-MM-yyyy')}</strong>), which is 3 days away.</p>

<p  style='margin: 5px 0px;'>Your timely input is crucial in promoting transparency, ensuring compliance, and enhancing ESG performance across our value chain. We encourage you to complete the self-assessment at your earliest convenience to avoid last-minute delays.</p>
   <p style='margin: 5px 0px;'>please log in to the  <a href="https://tvsmotor-supplier.eisqr.com" target="_blank">EiSqr – ESG Platform</a> to complete the self-assessment.</p>
<p  style='margin: 5px 0px;'> For any support or queries, please reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a>, copying <a href="mailto:<EMAIL>"><EMAIL></a>.We sincerely appreciate your cooperation and commitment to sustainability.</p>
<p>Warm regards,<br/>
<strong>TVS Motor Company Limited</strong></p>
<p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>`;
        }

        email.push({
          // email: [vendorSpoc.email],
          email: [vendorSpoc[0]?.email, ...supplierOtherSpoc],
          subject,
          body,
          cc
        });

      }
    }

    return [{
      id: 289, email
    }]
  }

  @get('calibration-reminder-mail')
  async sendCalibrationSupllierReminder(): Promise<any> {

    const utcStart = DateTime.utc().plus({days: 1}).startOf('day'); // Start of tomorrow (UTC)
    const utcEnd = utcStart.endOf('day'); // End of tomorrow (UTC)
    const email: any = []
    // Fetch the calibration assignments from the repository
    const calibrationAssignment = await this.userProfileRepository.supplierAssessmentAssignments(289).find(
      {

        "include": [
          {
            "relation": "supplierAssignmentSubmission", scope: {fields: {type: true, supplierMSIScore: true, submitted_on: true, id: true}}
          },


          {
            "relation": "auditorAssignmentSubmission", scope: {fields: {type: true, approved_on: true, approverComments: true, rejected_on: true, auditorMSIScore: true, submitted_on: true, modified_on: true, id: true}}
          },
          {
            "relation": "vendor"
          }
        ]

      }
    );
    const roleAgainsCategory = {
      1: 25,
      2: 26,
      3: 27,
      4: 28,
      5: 29,
      6: 30,
      7: 31,
      8: 32,
      9: 33,
      10: 34,
      11: 35
    }

    // Filter assignments based on the auditStartDate
    const filteredAssignment = calibrationAssignment.filter((item: any) => {
      // Ensure the auditStartDate is in UTC and then add 1 day


      const tomorrowDay = DateTime.utc().plus({days: 1}).day;

      const auditDate = DateTime.fromISO(item.auditStartDate || '', {zone: 'utc'}).plus({day: 1});

      return auditDate.isValid && auditDate.day === tomorrowDay;

    });


    for (const assignment of filteredAssignment) {

      const vendorData = await this.vendorCodeRepository.findById(assignment.vendorId)
      const vendorSpoc = await this.filteredUP({where: {id: vendorData.userProfileId}})

      if (vendorData && vendorSpoc) {
        const supplierOtherSpoc = this.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)
        const role_id = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
        const roles = await this.userRoleAuthorizationRepository.execute(

          `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify([role_id])]
        )
        const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
        const headUserData = await this.filteredUP({where: {id: {inq: headUserIds}}})
        const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
        const allAssessors = [
          assignment?.group1?.assessors,
          assignment?.group2?.assessors,
          assignment?.group3?.assessors,
          assignment?.group4?.assessors
        ].filter(assessors => Array.isArray(assessors)).flatMap(x => x).filter((id): id is number => id != null);
        const auditorData = await this.filteredUP({where: {id: {inq: allAssessors || []}}})

        const categoryList = [{name: 'Forging & Machining', value: 1}, {name: 'Casting & Machining', value: 2}, {name: 'Pressing & Fabrication', value: 3}, {name: 'Proprietary Mechanical', value: 4}, {name: 'Proprietary Electrical', value: 5}, {name: 'Plastics, Rubber, Painting and Stickers', value: 6}, {name: 'EV/3W/2W', value: 7}, {name: 'BW', value: 8}, {name: 'Accessories', value: 9}, {name: 'IDM (Indirect Material)', value: 10}, {name: 'Import', value: 11}]
        const supplierCategoryName =
          categoryList.find(item => item.value === vendorData.supplierCategory)?.name || 'N/A';

        if (auditorData.length) {
          const subject = `Reminder: MSI Calibration Audit Scheduled for Tomorrow - ${vendorData.supplierName}`
          const body = `<p>Dear Auditor(s) and ${vendorData.supplierName} </p>

  <p  style="margin: 5px 0px;" >Greetings from TVS Motors </p>
    <p style="margin: 5px 0px;" >This is a gentle reminder that the <strong>MSI Calibration Audit</strong> for the following dealership is scheduled for <strong>tomorrow</strong>. Please find the audit details below: </p>

        <p  style="margin: 5px 0px;" ><strong> Supplier Details :</strong> </p>
      <ul>
<li>Supplier Name :<strong>${vendorData.supplierName}</strong> </li>
<li>Supplier Code :<strong>${vendorData.code}</strong> </li>
<li>Supplier Category :<strong>${supplierCategoryName}</strong> </li>
 <li>Supplier Location :<strong>${vendorData.supplierLocation}</strong> </li>
   <li>Supplier Spoc Name :<strong>${vendorData.supplierSPOC}</strong> </li>
      </ul>

         <p   ><strong> Audit Schedule :</strong> </p>
      <ul>
<li>Scheduled Date :<strong>${DateTime.fromISO(assignment?.auditStartDate ? assignment.auditStartDate : '', {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy')}</strong> </li>
${auditorData?.map((item: any, index: number) => {
            return (
              `<li>Auditor Name #${index + 1} :<strong>${item?.information?.empname || 'NA'}</strong> </li>
<li>Auditor Email Id #${index + 1}:<strong>${item?.email || 'NA'}</strong> </li>`

            )
          }).join('')}
      </ul>
        <p  style="margin: 5px 0px;" >We kindly request the dealership team (Sales Manager, Works Manager, HR & Finance) to ensure their presence and make the necessary arrangements for a smooth and effective audit. </p>

 <p  style="margin: 5px 0px;" >We also request the assigned auditor to be fully prepared with all required tools and materials to conduct the audit as scheduled.</p>



  <p  style="margin: 5px 0px;" >For any queries or assistance, feel free to reach out to us at <strong><EMAIL></strong> and copy <strong><EMAIL></strong></p>

   <p  style="margin: 5px 0px;" >Thank you for your cooperation.</p>
   <p>Best regards,,</p>
<p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
    <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
  `
          email.push({email: [...auditorData.map((x: any) => x.email), ...supplierOtherSpoc, vendorSpoc.email], subject, body, cc: [...headSpocMailId, '<EMAIL>', '<EMAIL>']})
        }
      }
    }
    return [{
      id: 289, email
    }]
  }
  //   @get('/action-closure')
  //   async actionClosureReminder(): Promise<any> {
  //     const today = new Date();
  //     const email: any = []


  //     const filteredActions = await this.supplierActionRepository.find({
  //       where: {
  //         and: [
  //           {categoryOfFinding: 3},
  //           {or: [{type: 1}, {type: 2}]},

  //         ],
  //       },


  //     });

  //     for (const action of filteredActions) {

  //       const dueDateStr = action.actionTargetDate;

  //       if (!dueDateStr) {
  //         console.log(`Skipping ID ${action.id} - No due date`);
  //         continue; // Skip if due date not set
  //       }

  //       const dueDate = new Date(dueDateStr);

  //       // Calculate the difference in days
  //       const diffInTime = dueDate.getTime() - today.getTime();
  //       const diffInDays = Math.floor(diffInTime / (1000 * 60 * 60 * 24));

  //       console.log(`Action ID: ${action.id}, Due Date: ${dueDateStr}, Days left: ${diffInDays}`);
  //       const actionVendor = action.supplierAssessmentAssignmentId
  //       const vendorRelation = await this.supplierAssessmentAssignmentRepository.findById(
  //         actionVendor,
  //         {
  //           include: [

  //             {relation: 'vendor'},
  //           ],
  //         }
  //       );

  //       const vendorData = await this.vendorCodeRepository.findById(vendorRelation.vendorId);
  //       const adminObj = await this.userProfileRepository.findById(289);

  //       if (diffInDays === 3 || diffInDays === 1) {
  //         const subject = `Reminder: Action Closure Due soon – ${vendorData.supplierName} (${vendorData.code})`;

  //         const body = `
  //       <p>Dear ${vendorData.supplierName},</p>

  //       <p style="margin: 10px 0px;">Hope you are doing well.</p>

  //       <p style="margin: 10px 0px;">
  //         This is a reminder that the due date for closing the action(s) committed in your approved Corrective Action Plan (CAP) is approaching.As per your submitted timeline on the Navigos Sustainability Platform, the closure is due in ${diffInDays} day(s) from today.
  //       </p>

  //       <p style="margin: 10px 0px;">
  //         Kindly ensure that the required updates and evidence are uploaded on the platform within the committed timeline to support closure and avoid delays in final assessment.
  //       </p>

  //        ${adminObj?.userPortalUrl ? `<p style="margin: 10px 0px;">
  //    please log in to the  <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a> to complete and submit the required action
  //  </p>` : ''}


  //       <p style="margin: 10px 0px;">
  //         For any queries or assistance, please feel free to contact <a href="mailto:<EMAIL>"><EMAIL></a>,
  //         copying <a href="mailto:<EMAIL>"><EMAIL></a>.
  //       </p>

  //       <p style="margin: 10px 0px;">Thank you for your cooperation.</p>

  //       <p style="margin: 10px 0px;">Warm regards,<br/>TVS Motor Company Limited</p>

  //        <p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>
  //     `;



  //         email.push({subject, body, })
  //       }
  //     }

  //     return [{
  //       id: 289, email
  //     }]
  //   }



  @get('action-plan-reminder')
  async actionPlanRminder(): Promise<any> {
    const today = new Date();
    const email: any = []
    const actionPlanNotify = await this.userProfileRepository.supplierAssessmentAssignments(289).find(
      {

        "include": [



          {
            "relation": "auditorAssignmentSubmission", scope: {fields: {type: true, approved_on: true, approverComments: true, rejected_on: true, auditorMSIScore: true, submitted_on: true, modified_on: true, id: true}}
          },
          {
            "relation": "vendor"
          }
        ]

      }
    );



    const filteredAssignments = actionPlanNotify.filter((item: any) => {
      const submission = item.auditorAssignmentSubmission;

      if (!submission || submission.type != null || submission.type !== 2) return false;
      if (item.actionPlanType === 1 || item.actionPlanType === 21) return false;

      const approvedOn = submission.approved_on
        ? new Date(submission.approved_on)
        : null;

      if (!approvedOn) return false;

      const daysSinceApproved =
        (today.getTime() - approvedOn.getTime()) / (1000 * 60 * 60 * 24);

      return daysSinceApproved >= 7;
    });

    // Send emails
    for (const item of filteredAssignments) {



      const roleAgainsCategory = {
        1: 25,
        2: 26,
        3: 27,
        4: 28,
        5: 29,
        6: 30,
        7: 31,
        8: 32,
        9: 33,
        10: 34,
        11: 35
      }
      const vendorData = await this.vendorCodeRepository.findById(item.vendorId);
      const role_id = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
      const roles = await this.userRoleAuthorizationRepository.execute(

        `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
        [JSON.stringify([role_id])]
      )
      const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
      const headUserData = await this.filteredUP({where: {id: {inq: headUserIds}}})
      const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
      const vendorSpoc = await this.filteredUP({where: {id: vendorData.userProfileId}});
      const supplierOtherSpoc = this.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)
      const supplierMailIds = [...supplierOtherSpoc, vendorSpoc?.[0]?.email].filter((x: any) => x)


      const approvedRaw = item.auditorAssignmentSubmission.approved_on;
      const approvedDate = approvedRaw ? new Date(approvedRaw).toLocaleDateString() : 'N/A';
      // const approvedDate = new Date(item.auditorAssignmentSubmission.approved_on).toLocaleDateString();

      const adminObj = await this.userProfileRepository.findById(item.userProfileId);

      const subject = `Reminder: Action Plan Submission Pending ${vendorData.supplierName} (${vendorData.code})`;

      const body = ` <p>Dear ${vendorData.supplierName},</p>
      <p style="margin: 10px 0px;" >Hope you are doing well.</p>

      <p style="margin: 10px 0px;" >This is a reminder that we have not yet received your <strong>Corrective Action Plan</strong> following the audit report shared with you on <strong>${approvedDate}</strong> via the <strong>Navigos Sustainability Platform</strong>.</p>

      <p style="margin: 10px 0px;" >As per the audit process, the Corrective Action Plan —along with the <strong>Root Cause Analysis (RCA)</strong>— is expected to be submitted within <strong>7 days of receiving the audit report</strong>.<br/>
      As it has now been 7 days, we kindly request you to share your response at the earliest to avoid any delays in the closure process.</p>


       ${adminObj?.supplierPortalUrl ? `<p style="margin: 10px 0px;">
   please log in to the  <a href=${adminObj?.supplierPortalUrl} target="_blank">EiSqr – ESG Platform</a> to complete and submit the required action
 </p>` : ''}

      <p style="margin: 10px 0px;" >Your timely response is essential in ensuring continuous improvement and maintaining your sustainability rating. For any assistance, please feel free to reach <NAME_EMAIL>, copying <EMAIL></p>

      <p style="margin: 10px 0px;" >Thank you for your prompt attention to this matter.</p>

      <p style="margin: 10px 0px;" >Warm regards,<br/>
      TVS Motor Company Limited</p>

        <p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>
    `;
      email.push({email: [...supplierMailIds], subject, body, cc: [...headSpocMailId, '<EMAIL>', '<EMAIL>']})
    }

    return [{
      id: 289, email
    }]

  }
  @post('/user-profiles/{id}/get-assigned-indicator-list')
  async getAssignedIndicator(
    @param.path.number('id') id: number, @requestBody({
      required: false,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              framework: {
                type: 'array',
                items: {type: 'string'},
              },
              indicatorId: {
                type: 'array',
                items: {type: 'number'},
              },
            },
          },
        },
      },
    })
    requestBody: {
      framework?: string[];
      indicatorId?: number[];
    }
  ): Promise<any> {
    const assingedIndicator = await this.userProfileRepository.assignDcfClients(id).find()
    const framework = requestBody?.framework ? requestBody.framework : ['']
    const indicatorId = requestBody?.indicatorId || [0]

    if (assingedIndicator && assingedIndicator.length) {
      const indicator_list: any = []
      const assignedIndicator = assingedIndicator[0]
      const esgCategory = await this.newCategoryRepository.find({
        include: [
          {
            relation: "newTopics",
            scope: {
              include: [{
                relation: "newMetrics", scope: {
                  include: ["newDataPoints"],
                }
              }],
            },
          },
        ],
      });
      const shapedCategory = esgCategory.map(item => {
        if (item.newTopics) {
          item.newTopics = item.newTopics.filter(topics =>
            topics.newMetrics && topics.newMetrics.length > 0
          );
        }
        return item;
      }).filter(item => item.newTopics && item.newTopics.length > 0)
      shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
        if (assignedIndicator.topic_ids && top.id && assignedIndicator.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === id)) {
          top.newMetrics.forEach((met) => {

            if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0) && met.id && assignedIndicator.metric_ids && assignedIndicator.metric_ids.includes(met.id) && !indicator_list.map((i: any) => i.id).includes(met.id) && (met.tag === null || parseFloat(met.tag) === id)) {
              indicator_list.push({
                ...met, frameworkTag: [
                  ...(met?.data1?.[0]?.tags1 || []),
                  ...(met?.data1?.[0]?.tags2 || []),
                  ...(met?.data1?.[0]?.tags3 || []),
                  ...(met?.data1?.[0]?.tags4 || [])
                ]
              })

            }
          })
        }
      })
      // dcfIds,standaloneIds,sapIds,framework
      const transformedIndicatorList = await this.filterDerivedAndStandaloneWithIds(indicator_list, shapedCategory.flatMap(x => x?.newTopics?.flatMap(y => y?.newMetrics || []) || []), id)

      return transformedIndicatorList.filter(x =>
        (indicatorId.includes(x.id) || indicatorId.includes(0)) &&
        (
          (framework.length === 1 && framework[0].trim() === '') || !x.frameworkTag.length ||
          x.frameworkTag?.some((tag: string) =>
            framework.some((searchTerm: string) =>
              tag.trim().toLowerCase().includes(searchTerm.trim().toLowerCase())
            )
          )
        )
      )

    } else {
      console.log('Empty')
      return []
    }
  }
  @post('/user-profiles/{id}/get-assigned-dcf-list')
  async getAssignedDcfs(
    @param.path.number('id') id: number, @requestBody({
      required: false,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              framework: {
                type: 'array',
                items: {type: 'string'},
              },
              year: {
                type: 'object',
                properties: {
                  startMonth: {type: 'string'},
                  endMonth: {type: 'string'},
                },
              },
              indicatorId: {
                type: 'array',
                items: {type: 'number'},
              },
            },
          },
        },
      },
    })
    requestBody: {
      framework?: string[];
      year?: {
        startMonth?: string;
        endMonth?: string;
      };
      indicatorId?: number[];
    }
  ): Promise<any> {
    const framework = requestBody?.framework ? requestBody.framework : ['']
    const indicatorId = requestBody?.indicatorId || [0]
    const {startMonth, endMonth} = requestBody?.year || {startMonth: "Jan-" + DateTime.utc().year, endMonth: "Dec-" + DateTime.utc().year}
    const transformedIndicatorList = await this.getAssignedIndicator(id, {framework, indicatorId})

    const requiredDcfs = transformedIndicatorList.map((x: any) => x?.dcfIds || []).flatMap((x: any) => x).filter((v: any, i: any, a: any) => a.indexOf(v) === i)
    const entityAssignment = await this.userProfileRepository.assignDcfEntities(id).find({where: {dcfId: {inq: requiredDcfs}}})
    const entityUserAssingment = await this.userProfileRepository.assignDcfEntityUsers(id).find({where: {dcfId: {inq: requiredDcfs}, entityAssId: {inq: entityAssignment.map(x => x.id)}}})
    const monthFilteredUserAssignment = this.helper.filterDCFAssignmentsByMonthRange(entityUserAssingment, startMonth || "Jan-" + DateTime.utc().year, endMonth || "Dec-" + DateTime.utc().year)
    const locations = await this.userProfileRepository.locationOnes(id).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})
    const shapedSite = locations.map(item => {
      if (item.locationTwos) {
        item.locationTwos = item.locationTwos.filter(locationTwo =>
          locationTwo.locationThrees && locationTwo.locationThrees.length > 0
        );
      }
      return item;
    }).filter(item => item.locationTwos && item.locationTwos.length > 0);
    const locations0 = [0]
    const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
    const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
    const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
    const locationMap: any = {
      0: locations0,
      1: locations1,
      2: locations2,
      3: locations3
    };

    const filteredAssignments = monthFilteredUserAssignment.filter((assignment) => {
      return entityAssignment.some((ent: any) => {
        const tierKey = `tier${assignment.level}_ids`;
        const validLocations = locationMap[assignment?.level || 0] || [];

        // Filter out invalid IDs from ent[tierKey]
        const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
          validLocations.includes(Number(id))
        );



        // Check if assignment.locationId is present in the filtered IDs
        const isLocationMatch = filteredTierIds.includes(Number(assignment.locationId));

        const isBasicMatch =
          ent.dcfId === assignment.dcfId

        return isBasicMatch && isLocationMatch;
      });
    });
    const indicatorAssignment = await this.userProfileRepository.indicatorApproverAssignments(id).find()

    const result = this.mapApproverIds(indicatorAssignment.map((i: any) => ({...i, dcfIds: transformedIndicatorList.find((x: any) => x.id === i.indicatorId && x.standalone_ids.length === 1)?.dcfIds || []})), filteredAssignments.map(x => ({...x, tags: transformedIndicatorList.filter((z: any) => z.dcfIds.includes(x.dcfId)).map((y: any) => y.frameworkTag).flat(), approver_ids: []})), shapedSite)
    return {dcfAssignment: result, indicatorAssignment: transformedIndicatorList}

  }
  @post('/user-profiles/{id}/get-dcf-status-by-indicator')
  async getDcfStatusByIndicators(
    @param.path.number('id') id: number, @requestBody({
      required: false,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              framework: {
                type: 'array',
                items: {type: 'string'},
              },
              year: {
                type: 'object',
                properties: {
                  startMonth: {type: 'string'},
                  endMonth: {type: 'string'},
                },
              },
              indicatorId: {
                type: 'array',
                items: {type: 'number'},
              },
            },
          },
        },
      },
    })
    requestBody: {
      framework?: string[];
      year?: {
        startMonth?: string;
        endMonth?: string;
      };
      indicatorId?: number[];
    }
  ): Promise<any> {
    const framework = requestBody?.framework ? requestBody.framework : ['']
    const indicatorId = requestBody?.indicatorId || [0]
    const startMonth = requestBody?.year?.startMonth ?? `Jan-${DateTime.utc().year}`;
    const endMonth = requestBody?.year?.endMonth ?? `Dec-${DateTime.utc().year}`;

    const filteredLocations = await this.userProfileRepository.locationOnes(id).find({
      include: [
        {
          relation: "locationTwos",
          scope: {
            include: [{relation: "locationThrees"}],
          },
        },
      ],
    });

    const shapedSite = filteredLocations.map(item => {
      if (item.locationTwos) {
        item.locationTwos = item.locationTwos.filter(locationTwo =>
          locationTwo.locationThrees && locationTwo.locationThrees.length > 0
        );
      }
      return item;
    }).filter(item => item.locationTwos && item.locationTwos.length > 0);
    const assignedIndicatorAndDcf = await this.getAssignedDcfs(id, {framework, indicatorId, year: {startMonth, endMonth}})
    const assignedDcf = assignedIndicatorAndDcf?.dcfAssignment || []
    const assignedIndicator = assignedIndicatorAndDcf?.indicatorAssignment || []

    const submittedData = await this.userProfileRepository.structuredResponses(id).find({
      include: [
        {
          relation: "submitDcf",
          scope: {
            fields: {
              id: true,
              return_remarks: true,
              approved_on: true,
              locationId: true,
              level: true,
              reporter_modified_by: true,
              reporter_modified_on: true,
              reviewer_modified_on: true,
              reviewer_modified_by: true,
              self: true,
              approved_by: true,
              reject: true,
              type: true,
              edit: true,
            },
          },
        },
      ], where: {dcfId: {inq: assignedDcf.map((x: any) => x.dcfId)}}
    })
    const filteredStructuredResponse = this.helper.filterByReportingPeriodByMonthRange(submittedData.filter((x: any) => x.submitDcf && (x.submitDcf.type === 2 || x.submitDcf.type === 3)), startMonth || "Jan-" + DateTime.utc().year, endMonth || "Dec-" + DateTime.utc().year)
    const requireDps = filteredStructuredResponse.filter((x: any) => x.isManualForm).map(y => y.uniqueId)
    const datapoint = await this.newDataPointRepository.find({where: {suffix: {inq: requireDps}}})
    let dataPointUnitList = datapoint.filter(x => Array.isArray(x.data1) && x.data1.length).map((x: any) => ({dp: x.suffix, unit: x?.data1[0]?.unit || 'N A'}))

    const result = await Promise.all(
      filteredStructuredResponse.map(async (i) => ({
        formCategory: 1,
        formId: i.dcfId,
        value: i.isNull ? 0 : i.value,
        actualTitle: i.title,
        title:
          i.label
            ?.replace(/(<([^>]+)>)/gi, "")
            ?.replace(/\n/g, " ")
            ?.replace(/&nbsp;/g, " ")
            ?.replace("&amp;", "&") || "-",
        approverComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 3)?.remarks || "No Comments",
        dateOfApproval: i.submitDcf?.approved_on
          ? new Date(i.submitDcf?.approved_on).toLocaleString().split(",")[0]
          : "-",
        dcfId: i.dcfId,
        conversionValue: i?.conversionValue || null,
        entity: ((await this.getSortedEntity(i.submitDcf?.level, i.submitDcf?.locationId, shapedSite)) as any)?.name || 'NA',
        periodFrom: i.reporting_period?.[0] || "N/A",
        periodTo: i.reporting_period?.[i.reporting_period?.length - 1] || "N/A",
        unitOfMeasure: i.isManualForm ? (dataPointUnitList.find(x => x.dp === i.uniqueId)?.unit || '-') : i?.uom || "-",
        dataType: i?.dataType || null,
        formType: i?.formType || null,
        uniqueId: i?.uniqueId || null,
        locationId: i.submitDcf?.locationId,
        level: i.submitDcf?.level,
        reporter: `${await this.getUserByUPID(i.submitDcf?.reporter_modified_by)}`,
        reportedDate: new Date(i.submitDcf?.reporter_modified_on)
          .toLocaleString()
          .split(",")[0],
        reporting_period: getRPTextFormat(i.reporting_period),
        rp: i.reporting_period,
        reviewedDate: i.submitDcf?.reviewer_modified_on
          ? new Date(i.submitDcf?.reviewer_modified_on).toLocaleString().split(",")[0]
          : "-",
        reporterComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 1)?.remarks || "No Comments",
        reviewer: i.submitDcf?.self
          ? "Self"
          : `${await this.getUserByUPID(i.submitDcf?.reviewer_modified_by)}`,
        efValue: i.efValue,
        submitId: i.submitDcfId,
        reviewerComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 2)?.remarks || "No Comments",
        approver: i.submitDcf?.approved_by
          ? `${await this.getUserByUPID(i.submitDcf?.approved_by)}`
          : "N/A",
        status:
          i.submitDcf?.type === 1
            ? "Under Review"
            : i.submitDcf?.type === 2
              ? "Under Approval"
              : i.submitDcf?.type === 3
                ? "Approved"
                : i.submitDcf?.type === 0
                  ? "Draft"
                  : "Pending Submission",
      }))
    )

    // const mappingSubmission = this.helper.generateReportingPeriods(startMonth || '', endMonth || '', assignedDcf, result)
    // for (const item of mappingSubmission) {
    //   if (!item.entity) {

    //     item.entity = ((await this.getSortedEntity(item?.level, item?.locationId, shapedSite)) as any)?.name || 'NA'
    //   }
    // }
    const startDate = DateTime.fromFormat(startMonth, "LLL-yyyy").startOf('day').toFormat('ddMMyyyy')
    const endDate = DateTime.fromFormat(endMonth, "LLL-yyyy").startOf('day').toFormat('ddMMyyyy')
    const sapFilteredResponse = await this.sapResponseController.fetchResponse({startDate, endDate}, id, {where: {sapId: {inq: ['SAP4', 'SAP5', 'SAP7']}}})
    // const mappingSap = sapFilteredResponse?.data?.map((item: any) => ({
    //   formId: item.sapId, formCategory: 2,
    //   dataPoint: item.sapId === 'SAP1' ? item.FuelType : (item.sapId === 'SAP2' || item.sapId === 'SAP3') ? item.WasteDescription : (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? item.MaterialCategory : item.sapId === 'SAP6' ? item.BTOrigin + ' to ' + item.BTDestination : item.sapId === 'SAP7' ? item.ModeOfTransportation
    //     : item.sapId === 'SAP8' ? item.EmpId : 'No', //NeedToConfirm
    //   value: (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? item.TotalSpent : item.sapId === 'SAP7' ? item.InvoiceAmount : (item?.Quantity || '-'),
    //   unitOfMeasure: item.UoM || 'NA',
    //   status: "Approved",
    //   entity: item.Plant || 'NA',
    //   rp: [item.Date ? this.convertDateFormat(item.Date) : null].filter(x => x),
    //   periodFrom: this.convertDateFormat(item.Date),
    //   periodTo: this.convertDateFormat(item.Date),
    //   syncDate: this.formatIsoDate(item.fetched_on),
    // })) || []
    const mappingSap = sapFilteredResponse?.data?.filter((x: any) => x.sapId === 'SAP4' || x.sapId === 'SAP5' || x.sapId === 'SAP7')?.map((x: any) => ({...x, sapId: x.dataType, unitOfMeasure: "USD", title: x.sapId === 'SAP7' ? x.ModeOfTransportation : x.MaterialCategory, periodFrom: this.getRPLuxon(x.Month)?.[0], periodTo: this.getRPLuxon(x.Month)?.[0], value: x.sapId === 'SAP7' ? parseFloat(x?.Distance ? (x.Distance.toFixed(3)) : 0) : parseFloat(x?.TotalSpent ? (x.TotalSpent.toFixed(3)) : 0), reporting_period: x.Month, rp: this.getRPLuxon(x.Month), entity: this.getCoverageText(this.getLevelAndLocationId(x), shapedSite), ...this.getLevelAndLocationId(x)})) || []
    // const mergeSubmissionWithAssignment = this.flattenReportingResults(mappingSubmission)

    const sapHRData = id === 289 ? await this.employeeDataController.getEmployeeAnalytics({type: 1, fromDate: startDate, toDate: endDate}) : []
    const retentionData = id === 289 ? await this.employeeDataController.getRetentionRateByPeriod({type: 1, fromDate: startDate, toDate: endDate}) : []
    const attritionData = id === 289 ? await this.employeeDataController.getAttritionRateByPeriod({type: 1, fromDate: startDate, toDate: endDate}) : []
    const assignedEF = await this.clientEfCategoryMappingController.getClientEfCategoryMappingsCustom(id)
    const shapedEFAssignment = await this.processSingleObject(assignedEF, shapedSite)

    const refinedIndicator = (assignedIndicator as any[])?.map(({newDataPoints, ...item}) => ({...item, title: item.id + ' : ' + item.title, type: item?.standalone_ids?.length === 1 && item.standalone_ids.includes(item.id) ? 1 : ((item?.standalone_ids?.length === 1 && !item.standalone_ids.includes(item.id)) || (item?.standalone_ids?.length > 1)) ? 2 : 0}))
    const computedData = await this.processCustomMetrics(refinedIndicator, [...sapHRData.map((x: any) => ({...x, efvalue: x.value})), ...retentionData.map((x: any) => ({...x, indicatorId_: 693})), ...attritionData.map((x: any) => ({...x, indicatorId_: 695})), ...result, ...mappingSap], shapedEFAssignment, shapedSite)
    return computedData
  }
  @post('/user-profiles/{id}/get-computed-structured-response')
  async getIndicatorDataApprovalIndicators(
    @param.path.number('id') id: number, @requestBody({
      required: false,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              framework: {
                type: 'array',
                items: {type: 'string'},
              },
              year: {
                type: 'object',
                properties: {
                  startMonth: {type: 'string'},
                  endMonth: {type: 'string'},
                },
              },
              indicators: {
                type: 'object',
                items: {type: 'number'},
              },
            },
          },
        },
      },
    })
    requestBody: {
      framework?: string[];
      year?: {
        startMonth?: string;
        endMonth?: string;
      };
      indicators?: number[];
    }
  ): Promise<any> {
    const framework = requestBody?.framework ? requestBody.framework : ['']
    const indicatorId = requestBody?.indicators || [0]
    const startMonth = requestBody?.year?.startMonth ?? `Jan-${DateTime.utc().year}`;
    const endMonth = requestBody?.year?.endMonth ?? `Dec-${DateTime.utc().year}`;

    const filteredLocations = await this.userProfileRepository.locationOnes(id).find({
      include: [
        {
          relation: "locationTwos",
          scope: {
            include: [{relation: "locationThrees"}],
          },
        },
      ],
    });

    const shapedSite = filteredLocations.map(item => {
      if (item.locationTwos) {
        item.locationTwos = item.locationTwos.filter(locationTwo =>
          locationTwo.locationThrees && locationTwo.locationThrees.length > 0
        );
      }
      return item;
    }).filter(item => item.locationTwos && item.locationTwos.length > 0);
    const assignedIndicatorAndDcf = await this.getAssignedDcfs(id, {framework, indicatorId, year: {startMonth, endMonth}})
    const assignedDcf = assignedIndicatorAndDcf?.dcfAssignment || []
    const assignedIndicator = assignedIndicatorAndDcf?.indicatorAssignment || []

    const submittedData = await this.userProfileRepository.structuredResponses(id).find({
      include: [
        {
          relation: "submitDcf",
          scope: {
            fields: {
              id: true,
              return_remarks: true,
              approved_on: true,
              locationId: true,
              level: true,
              reporter_modified_by: true,
              reporter_modified_on: true,
              reviewer_modified_on: true,
              reviewer_modified_by: true,
              self: true,
              approved_by: true,
              reject: true,
              type: true,
              edit: true,
            },
          },
        },
      ], where: {dcfId: {inq: assignedDcf.map((x: any) => x.dcfId)}}
    })
    const filteredStructuredResponse = this.helper.filterByReportingPeriodByMonthRange(submittedData.filter((x: any) => x.submitDcf), startMonth || "Jan-" + DateTime.utc().year, endMonth || "Dec-" + DateTime.utc().year)
    const requireDps = filteredStructuredResponse.filter((x: any) => x.isManualForm).map(y => y.uniqueId)
    const datapoint = await this.newDataPointRepository.find({where: {suffix: {inq: requireDps}}})
    let dataPointUnitList = datapoint.filter(x => Array.isArray(x.data1) && x.data1.length).map((x: any) => ({dp: x.suffix, unit: x?.data1[0]?.unit || 'N A'}))

    const result = await Promise.all(
      filteredStructuredResponse.map(async (i) => ({
        formCategory: 1,
        formId: i.dcfId,
        value: i.isNull ? 0 : i.value,
        actualTitle: i.title,
        title:
          i.label
            ?.replace(/(<([^>]+)>)/gi, "")
            ?.replace(/\n/g, " ")
            ?.replace(/&nbsp;/g, " ")
            ?.replace("&amp;", "&") || "-",
        approverComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 3)?.remarks || "No Comments",
        dateOfApproval: i.submitDcf?.approved_on
          ? new Date(i.submitDcf?.approved_on).toLocaleString().split(",")[0]
          : "-",
        dcfId: i.dcfId,
        conversionValue: i?.conversionValue || null,
        entity: ((await this.getSortedEntity(i.submitDcf?.level, i.submitDcf?.locationId, shapedSite)) as any)?.name || 'NA',
        periodFrom: i.reporting_period?.[0] || "N/A",
        periodTo: i.reporting_period?.[i.reporting_period?.length - 1] || "N/A",
        unitOfMeasure: i.isManualForm ? (dataPointUnitList.find(x => x.dp === i.uniqueId)?.unit || '-') : i?.uom || "-",
        dataType: i?.dataType || null,
        formType: i?.formType || null,
        uniqueId: i?.uniqueId || null,
        locationId: i.submitDcf?.locationId,
        level: i.submitDcf?.level,
        reporter: `${await this.getUserByUPID(i.submitDcf?.reporter_modified_by)}`,
        reportedDate: new Date(i.submitDcf?.reporter_modified_on)
          .toLocaleString()
          .split(",")[0],
        reporting_period: getRPTextFormat(i.reporting_period),
        rp: i.reporting_period,
        reviewedDate: i.submitDcf?.reviewer_modified_on
          ? new Date(i.submitDcf?.reviewer_modified_on).toLocaleString().split(",")[0]
          : "-",
        reporterComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 1)?.remarks || "No Comments",
        reviewer: i.submitDcf?.self
          ? "Self"
          : `${await this.getUserByUPID(i.submitDcf?.reviewer_modified_by)}`,
        efValue: i.efValue,
        submitId: i.submitDcfId,
        reviewerComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 2)?.remarks || "No Comments",
        approver: i.submitDcf?.approved_by
          ? `${await this.getUserByUPID(i.submitDcf?.approved_by)}`
          : "N/A",
        status:
          i.submitDcf?.type === 1
            ? "Under Review"
            : i.submitDcf?.type === 2
              ? "Under Approval"
              : i.submitDcf?.type === 3
                ? "Approved"
                : i.submitDcf?.type === 0
                  ? "Draft"
                  : "Pending Submission",
      }))
    )

    // const mappingSubmission = this.helper.generateReportingPeriods(startMonth || '', endMonth || '', assignedDcf, result)
    // const startDate = DateTime.fromFormat(startMonth, "LLL-yyyy").startOf('day').toFormat('ddMMyyyy')
    // const endDate = DateTime.fromFormat(endMonth, "LLL-yyyy").startOf('day').toFormat('ddMMyyyy')
    // const sapFilteredResponse = await this.sapResponseController.fetchResponse({startDate, endDate}, id, {where: {sapId: {inq: ['SAP4', 'SAP5', 'SAP7']}}})
    // const mappingSap = sapFilteredResponse?.data?.map((item: any) => ({
    //   formId: item.sapId, formCategory: 2,
    //   dataPoint: item.sapId === 'SAP1' ? item.FuelType : (item.sapId === 'SAP2' || item.sapId === 'SAP3') ? item.WasteDescription : (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? item.MaterialCategory : item.sapId === 'SAP6' ? item.BTOrigin + ' to ' + item.BTDestination : item.sapId === 'SAP7' ? item.ModeOfTransportation
    //     : item.sapId === 'SAP8' ? item.EmpId : 'No', //NeedToConfirm
    //   value: (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? item.TotalSpent : item.sapId === 'SAP7' ? item.InvoiceAmount : (item?.Quantity || '-'),
    //   unitOfMeasure: item.UoM || 'NA',
    //   status: "Approved",
    //   entity: item.Plant || 'NA',
    //   rp: [item.Date ? this.convertDateFormat(item.Date) : null].filter(x => x),
    //   periodFrom: this.convertDateFormat(item.Date),
    //   periodTo: this.convertDateFormat(item.Date),
    //   syncDate: this.formatIsoDate(item.fetched_on),
    // })) || []
    // const mappingSap = sapFilteredResponse?.data?.filter((x: any) => x.sapId === 'SAP4' || x.sapId === 'SAP5' || x.sapId === 'SAP7')?.map((x: any) => ({...x, sapId: x.dataType, unitOfMeasure: "USD", title: x.sapId === 'SAP7' ? x.ModeOfTransportation : x.MaterialCategory, periodFrom: this.getRPLuxon(x.Month)?.[0], periodTo: this.getRPLuxon(x.Month)?.[0], value: x.sapId === 'SAP7' ? parseFloat(x?.InvoiceAmount ? (x.InvoiceAmount.toFixed(3)) : 0) : parseFloat(x?.TotalSpent ? (x.TotalSpent.toFixed(3)) : 0), reporting_period: x.Month, rp: this.getRPLuxon(x.Month), entity: this.getCoverageText(this.getLevelAndLocationId(x), shapedSite), ...this.getLevelAndLocationId(x)})) || []
    // const mergeSubmissionWithAssignment = this.flattenReportingResults(mappingSubmission)
    const assignedEF = await this.clientEfCategoryMappingController.getClientEfCategoryMappingsCustom(id)
    const shapedEFAssignment = await this.processSingleObject(assignedEF, shapedSite)
    const refinedIndicator = (assignedIndicator as any[])?.map(({newDataPoints, ...item}) => ({...item, title: item.id + ' : ' + item.title, type: item?.standalone_ids?.length === 1 && item.standalone_ids.includes(item.id) ? 1 : ((item?.standalone_ids?.length === 1 && !item.standalone_ids.includes(item.id)) || (item?.standalone_ids?.length > 1)) ? 2 : 0}))
    const computedData = await this.processCustomMetrics(refinedIndicator, [...result], shapedEFAssignment, shapedSite)
    return computedData
  }
  @post('/user-profiles/{id}/get-enterprise-raw-data')
  async getEnterpriseRawData(
    @param.path.number('id') id: number, @requestBody({
      required: false,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              framework: {
                type: 'array',
                items: {type: 'string'},
              },
              year: {
                type: 'object',
                properties: {
                  startMonth: {type: 'string'},
                  endMonth: {type: 'string'},
                },
              },
              indicatorId: {
                type: 'array',
                items: {type: 'number'},
              },
              userId: {
                type: 'number',
                description: 'User ID for role 3 location filtering. If userId equals clientId (path parameter), grants global access to all locations without role checks.'
              },
              locationFilter: {
                type: 'object',
                properties: {
                  tier1_id: {type: 'number', nullable: true},
                  tier2_id: {type: 'number', nullable: true},
                  tier3_id: {type: 'number', nullable: true},
                },
                description: 'Specific location filter (Country, City/Region, State/Site)'
              },
            },
          },
        },
      },
    })
    requestBody: {
      framework?: string[];
      year?: {
        startMonth?: string;
        endMonth?: string;
      };
      indicatorId?: number[];
      userId?: number;
      locationFilter?: {
        tier1_id?: number | null;
        tier2_id?: number | null;
        tier3_id?: number | null;
      };
    }
  ): Promise<any> {
    const framework = requestBody?.framework ? requestBody.framework : ['']
    const indicatorId = requestBody?.indicatorId || [0]
    const startMonth = requestBody?.year?.startMonth ?? `Jan-${DateTime.utc().year}`;
    const endMonth = requestBody?.year?.endMonth ?? `Dec-${DateTime.utc().year}`;
    const userId = requestBody?.userId;
    const locationFilter = requestBody?.locationFilter || {tier1_id: null, tier2_id: null, tier3_id: null};

    // Get user's role 3 location assignments if userId is provided
    let userRole3Locations: any[] = [];
    let hasGlobalAccess = false;

    if (userId) {
      // Check if userId and clientId are the same (Global Level Access)
      if (userId === id) {
        hasGlobalAccess = true;
        // Create a mock global assignment for consistent processing
        userRole3Locations = [{
          tier1_id: 0,
          tier2_id: null,
          tier3_id: null,
          user_id: userId,
          userProfileId: id,
          roles: [2]
        }];
      } else {
        // Use the same role assignment logic as the location filtering endpoint
        const sqlQuery = `
          SELECT * FROM UserRoleAuthorization
          WHERE user_id = ?
            AND userProfileId = ?
            AND roles IS NOT NULL
            AND JSON_LENGTH(roles) > 0
            AND JSON_CONTAINS(roles, ?, '$')
        `;

        const queryParams = [userId, id, JSON.stringify([2])];

        const roleResults = await this.userRoleAuthorizationRepository.execute(sqlQuery, queryParams);
        const rawRoleLocations = Array.isArray(roleResults) ? roleResults : [];

        // Parse the roles property from JSON string to array (same as location filtering endpoint)
        userRole3Locations = rawRoleLocations.map((record: any) => {
          if (record && record.roles) {
            try {
              // Parse roles if it's a string, otherwise keep as is
              record.roles = typeof record.roles === 'string' ? JSON.parse(record.roles) : record.roles;
            } catch (error) {
              // If parsing fails, keep the original value
              console.warn('Failed to parse roles JSON:', record.roles);
            }
          }
          return record;
        });

        console.log('getEnterpriseRawData - Role assignments found:', userRole3Locations.length);
        console.log('getEnterpriseRawData - Parsed assignments:', JSON.stringify(userRole3Locations, null, 2));
      }
    }

    // Get all locations for the user profile
    const allLocations = await this.userProfileRepository.locationOnes(id).find({
      include: [
        {
          relation: "locationTwos",
          scope: {
            include: [{relation: "locationThrees"}],
          },
        },
      ],
    });

    // Check if user has corporate level assignment (tier1_id = 0) WITH role 2
    const hasCorporateAssignment = userRole3Locations.some(assignment =>
      assignment.tier1_id === 0 &&
      assignment.tier2_id === null &&
      assignment.tier3_id === null &&
      assignment.roles &&
      Array.isArray(assignment.roles) &&
      assignment.roles.includes(2)
    );

    // Filter locations based on role 2 assignments and specific location filter
    let filteredLocations = await this.getFilteredLocations(id, {userId: userId || 0, roles: [2]})

    // if (userId && userRole3Locations.length > 0) {
    //   if (hasGlobalAccess || hasCorporateAssignment) {
    //     // Global access or corporate assignment: user can see all locations
    //     filteredLocations = allLocations;
    //   } else {
    //     // Filter locations based on role 2 assignments (includes assigned + all child levels)
    //     filteredLocations = this.filterAccessibleLocationsByRole3Assignments(allLocations, userRole3Locations);
    //   }
    // }
    console.log("filteredLocations", filteredLocations)
    // Apply specific location filter if provided (works for both global and role-based access)
    // if (locationFilter) {
    //   filteredLocations = this.applySpecificLocationFilter(filteredLocations, locationFilter, userRole3Locations, hasGlobalAccess || hasCorporateAssignment);
    // }

    const assignedIndicatorAndDcf = await this.getAssignedDcfs(id, {framework, indicatorId, year: {startMonth, endMonth}})
    const assignedDcf = assignedIndicatorAndDcf?.dcfAssignment || []
    const assignedIndicator = assignedIndicatorAndDcf?.indicatorAssignment || []

    const submittedData = await this.userProfileRepository.structuredResponses(id).find({
      include: [
        {
          relation: "submitDcf",
          scope: {
            fields: {
              id: true,
              return_remarks: true,
              approved_on: true,
              locationId: true,
              level: true,
              reporter_modified_by: true,
              reporter_modified_on: true,
              reviewer_modified_on: true,
              reviewer_modified_by: true,
              self: true,
              approved_by: true,
              reject: true,
              type: true,
              edit: true,
            },
          },
        },
      ], where: {dcfId: {inq: assignedDcf.map((x: any) => x.dcfId)}}
    })
    const filteredStructuredResponse = this.helper.filterByReportingPeriodByMonthRange(submittedData.filter((x: any) => x.submitDcf), startMonth || "Jan-" + DateTime.utc().year, endMonth || "Dec-" + DateTime.utc().year)
    const requireDps = filteredStructuredResponse.filter((x: any) => x.isManualForm).map(y => y.uniqueId)
    const datapoint = await this.newDataPointRepository.find({where: {suffix: {inq: requireDps}}})
    let dataPointUnitList = datapoint.filter(x => Array.isArray(x.data1) && x.data1.length).map((x: any) => ({dp: x.suffix, unit: x?.data1[0]?.unit}))

    const result = await Promise.all(
      filteredStructuredResponse.map(async (i) => ({
        formCategory: 1,
        formId: i.dcfId,
        value: i.isNull ? 0 : i.value,
        actualTitle: i.title,
        title:
          i.label
            ?.replace(/(<([^>]+)>)/gi, "")
            ?.replace(/\n/g, " ")
            ?.replace(/&nbsp;/g, " ")
            ?.replace("&amp;", "&") || "-",
        approverComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 3)?.remarks || "No Comments",
        dateOfApproval: i.submitDcf?.approved_on
          ? new Date(i.submitDcf?.approved_on).toLocaleString().split(",")[0]
          : "-",
        dcfId: i.dcfId,
        entity: ((await this.getSortedEntity(i.submitDcf?.level, i.submitDcf?.locationId, allLocations)) as any)?.name || 'NA',
        periodFrom: i.reporting_period?.[0] || "N/A",
        periodTo: i.reporting_period?.[i.reporting_period?.length - 1] || "N/A",
        unitOfMeasure: i.isManualForm ? (dataPointUnitList.find(x => x.dp === i.uniqueId)?.unit || '-') : i?.uom || "-",
        dataType: i?.dataType || null,
        formType: i?.formType || null,
        uniqueId: i?.uniqueId || null,
        locationId: i.submitDcf?.locationId,
        level: i.submitDcf?.level,
        reporter: `${await this.getUserByUPID(i.submitDcf?.reporter_modified_by)}`,
        reportedDate: new Date(i.submitDcf?.reporter_modified_on)
          .toLocaleString()
          .split(",")[0],
        reporting_period: getRPTextFormat(i.reporting_period),
        rp: i.reporting_period,
        conversionValue: i?.conversionValue || null,
        reviewedDate: i.submitDcf?.reviewer_modified_on
          ? new Date(i.submitDcf?.reviewer_modified_on).toLocaleString().split(",")[0]
          : "-",
        reporterComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 1)?.remarks || "No Comments",
        reviewer: await this.getReviewerName(i, assignedDcf),
        efValue: i.efValue,
        submitId: i.submitDcfId,
        reviewerComments:
          i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 2)?.remarks || "No Comments",
        approver: await this.getApproverName(i, assignedDcf),
        status:
          i.submitDcf?.type === 1
            ? "Pending Review"
            : i.submitDcf?.type === 2
              ? "Pending Approval"
              : i.submitDcf?.type === 3
                ? "Approved"
                : i.submitDcf?.type === 0
                  ? "Draft"
                  : "Pending Submission",
      }))
    )
    const startDate = DateTime.fromFormat(startMonth, "LLL-yyyy").startOf('day').toFormat('ddMMyyyy')
    const endDate = DateTime.fromFormat(endMonth, "LLL-yyyy").startOf('day').toFormat('ddMMyyyy')
    const sapFilteredResponse = await this.sapResponseController.fetchResponse({startDate, endDate}, id, {where: {sapId: {inq: ['SAP4', 'SAP5', 'SAP7']}}})
    const sapHRData = id === 289 ? await this.employeeDataController.getEmployeeAnalytics({type: 1, fromDate: startDate, toDate: endDate}) : []

    const mappingSap = sapFilteredResponse?.data?.map((item: any) => ({
      formId: item.sapId, formCategory: 2,
      dataPoint: item.sapId === 'SAP1' ? item.FuelType : (item.sapId === 'SAP2' || item.sapId === 'SAP3') ? item.WasteDescription : (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? item.MaterialCategory : item.sapId === 'SAP6' ? item.BTOrigin + ' to ' + item.BTDestination : item.sapId === 'SAP7' ? item.ModeOfTransportation
        : item.sapId === 'SAP8' ? item.EmpId : 'No', //NeedToConfirm
      value: (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? parseFloat(item?.TotalSpent ? item.TotalSpent.toFixed(3) : 0) : item.sapId === 'SAP7' ? parseFloat(item?.Distance ? item.Distance.toFixed(3) : 0) : (parseFloat(item?.Quantity ? item.Quantity.toFixed(3) : 0) || '-'),
      unitOfMeasure: item.UoM || 'NA',
      status: "Approved",
      entity: item.Plant || 'NA', level: item.level, locationId: item.locationId,
      rp: [item.Date ? this.convertDateFormat(item.Date) : null].filter(x => x),
      periodFrom: this.convertDateFormat(item.Date),
      periodTo: this.convertDateFormat(item.Date),
      syncDate: this.formatIsoDate(item.fetched_on),
    })) || []


    const dcfLevels = new Map();
    const dcfLocationIds = new Map();
    const dcfEntities = new Set();
    result.forEach(item => {
      const level = item.level;
      const locationId = item.locationId;
      const entity = item.entity;

      dcfLevels.set(level, (dcfLevels.get(level) || 0) + 1);
      dcfLocationIds.set(locationId, (dcfLocationIds.get(locationId) || 0) + 1);
      dcfEntities.add(entity);
    });

    const sapLevels = new Map();
    const sapLocationIds = new Map();
    const sapEntities = new Set();
    mappingSap.forEach((item: any) => {
      const level = item.level;
      const locationId = item.locationId;
      const entity = item.entity;

      if (level !== undefined) sapLevels.set(level, (sapLevels.get(level) || 0) + 1);
      if (locationId !== undefined) sapLocationIds.set(locationId, (sapLocationIds.get(locationId) || 0) + 1);
      if (entity) sapEntities.add(entity);
    });

    const hrLevels = new Map();
    const hrLocationIds = new Map();
    const hrEntities = new Set();
    sapHRData.forEach(item => {
      const level = item.level;
      const locationId = item.locationId;
      const entity = item.entity;

      hrLevels.set(level, (hrLevels.get(level) || 0) + 1);
      hrLocationIds.set(locationId, (hrLocationIds.get(locationId) || 0) + 1);
      hrEntities.add(entity);
    });


    // Apply different filtering logic based on whether specific location filter is provided
    let filteredResult, filteredMappingSap, filteredSapHRData;

    console.log('=== MAIN FILTERING DECISION ===');
    console.log('locationFilter:', locationFilter);

    const hasSpecificFilter = locationFilter && (
      // Corporate level: tier1_id = 0
      (locationFilter.tier1_id === 0) ||
      // Country level: tier1_id > 0, tier2_id = 0
      (locationFilter.tier1_id !== undefined && locationFilter.tier1_id !== null && locationFilter.tier1_id > 0 && locationFilter.tier2_id === 0) ||
      // City level: tier1_id > 0, tier2_id > 0, tier3_id = 0
      (locationFilter.tier1_id !== undefined && locationFilter.tier1_id !== null && locationFilter.tier1_id > 0 &&
        locationFilter.tier2_id !== undefined && locationFilter.tier2_id !== null && locationFilter.tier2_id > 0 && locationFilter.tier3_id === 0) ||
      // BU level: tier1_id > 0, tier2_id > 0, tier3_id > 0
      (locationFilter.tier1_id !== undefined && locationFilter.tier1_id !== null && locationFilter.tier1_id > 0 &&
        locationFilter.tier2_id !== undefined && locationFilter.tier2_id !== null && locationFilter.tier2_id > 0 &&
        locationFilter.tier3_id !== undefined && locationFilter.tier3_id !== null && locationFilter.tier3_id > 0)
    );

    console.log('hasSpecificFilter:', hasSpecificFilter);
    console.log('Condition checks:');
    console.log('  Corporate (tier1_id === 0):', locationFilter?.tier1_id === 0);

    if (hasSpecificFilter) {
      // Specific location filtering: filter data based on the specific location filter
      filteredResult = this.filterDataBySpecificLocation(result, locationFilter, filteredLocations);
      filteredMappingSap = this.filterDataBySpecificLocation(mappingSap, locationFilter, filteredLocations);
      filteredSapHRData = this.filterDataBySpecificLocation(sapHRData, locationFilter, filteredLocations);
    } else {
      // Role-based filtering: filter data based on user role assignments
      filteredResult = this.filterDataBySpecificLocation(result, locationFilter, filteredLocations);
      filteredMappingSap = this.filterDataBySpecificLocation(mappingSap, locationFilter, filteredLocations);
      filteredSapHRData = this.filterDataBySpecificLocation(sapHRData, locationFilter, filteredLocations);
    }

    return [...filteredResult, ...filteredMappingSap, ...filteredSapHRData]
  }
  @get('/user-profiles/{id}/get-assigned-ef')
  async getAssignedEmissionFactor(
    @param.path.number('id') id: number): Promise<any> {

    const filteredLocations = await this.userProfileRepository.locationOnes(id).find({
      include: [
        {
          relation: "locationTwos",
          scope: {
            include: [{relation: "locationThrees"}],
          },
        },
      ],
    });

    const shapedSite = filteredLocations.map(item => {
      if (item.locationTwos) {
        item.locationTwos = item.locationTwos.filter(locationTwo =>
          locationTwo.locationThrees && locationTwo.locationThrees.length > 0
        );
      }
      return item;
    }).filter(item => item.locationTwos && item.locationTwos.length > 0);
    const assignedEF = await this.clientEfCategoryMappingController.getClientEfCategoryMappingsCustom(id)
    const shapedEFAssignment = await this.processSingleObject(assignedEF, shapedSite)
    return shapedEFAssignment
  }
  convertDateFormat(dateString: any) {
    if (dateString)
      return DateTime.fromFormat(dateString, "yyyyMMdd").toFormat("MM-yyyy");
    return null;
  };
  formatIsoDate(dateString: any) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB");
  };
  getRPLuxon(months: any) {

    if (months.includes('to')) {
      let startDate = DateTime.fromFormat(months.split('to')[0].trim(), 'LLL-yyyy')
      let endDate = DateTime.fromFormat(months.split('to')[1].trim(), 'LLL-yyyy')
      let rp = []
      while (startDate <= endDate) {
        rp.push(startDate.toFormat('LL-yyyy'));
        startDate = startDate.plus({months: 1})
      }
      return rp
    } else {
      return [DateTime.fromFormat(months, 'LLL-yyyy').toFormat('LL-yyyy')]
    }
  }
  selfAssessmentMonthTemplate(dealerData: any, dealerSelfSubmissions: any) {
    if (!Array.isArray(dealerSelfSubmissions)) return 'NA';

    const matched = dealerSelfSubmissions
      .filter(sub => sub.dealerId === dealerData.dealerId)
      .sort((a, b) => {
        const dateA: any = moment(a.reporting_period?.[0], 'MM-YYYY');
        const dateB: any = moment(b.reporting_period?.[0], 'MM-YYYY');
        return dateB - dateA;
      });

    if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return 'NA';

    return moment(matched[0].reporting_period[0], 'MM-YYYY').format('MMMM YYYY');
  };
  extractDealerHeadValidEmails(
    data: Record<string, any>,
    allowedObjectKeys: string[],
    validKeyIncludes: string[] = ['mailid']
  ): any[] {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    const emails = allowedObjectKeys
      .map(key => data[key])
      .filter(val => typeof val === 'object' && val !== null && !Array.isArray(val))
      .flatMap(obj =>
        Object.entries(obj)
          .filter(
            ([key, val]) =>
              validKeyIncludes.some(allowedKey => key.toLowerCase().includes(allowedKey.toLowerCase())) &&
              typeof val === 'string' &&
              emailRegex.test(val)
          )
          .map(([_, val]) => val)
      );

    // Remove duplicates using Set
    return Array.from(new Set(emails));
  }

  groupByHeadMailIds(
    data: GroupDealerHeaderMail[]
  ): Record<string, Omit<GroupDealerHeaderMail, 'headMailIds'>[]> {
    return data.reduce((acc, item) => {
      const {headMailIds, ...rest} = item;
      const emails = Array.isArray(headMailIds) && headMailIds.length > 0
        ? headMailIds
        : ['unknown'];

      emails.forEach(email => {
        if (!acc[email]) acc[email] = [];
        acc[email].push(rest);
      });

      return acc;
    }, {} as Record<string, Omit<GroupDealerHeaderMail, 'headMailIds'>[]>);
  }
  flattenReportingResults(results: any[]): any[] {
    const flattened: any[] = [];

    for (const entry of results) {
      const {
        reporting_period,
        data,
        reporter_ids,
        reviewer_ids,
        approver_ids,
        frequency, tags
      } = entry;

      if (data.length > 0) {
        for (const item of data) {
          flattened.push({
            ...item,
            reporting_period,
            reporter_ids,
            reviewer_ids,
            approver_ids,
            frequency, tags
          });
        }
      } else {
        flattened.push({...entry, "status": "Pending Submission"}); // cast for empty data fallback
      }
    }

    return flattened;
  }
  getLevelAndLocationId(data: any) {
    if (data.tier3_id) return {level: 3, locationId: Number(data.tier3_id)};
    if (data.tier2_id) return {level: 2, locationId: Number(data.tier2_id)};
    if (data.tier1_id) return {level: 1, locationId: Number(data.tier1_id)};
    if (data.tier0_id === 0) return {level: 0, locationId: 0};
    return null;
  };
  processSingleObject(locations: any[], locationData: any) {
    return locations
      .map(
        ({
          tier1_id,
          tier2_id,
          tier3_id,
          efGhgCat,
          efStandard,
          efCategory,
          efGhgSubCat,
          hierarchicalData,
          efGhgCatId,
          efStandardId,
          efCategoryId,
          efGhgSubCatId,
        }) => {
          let tier1 = "Global";
          let tier2 = "All";
          let tier3 = "All";

          if (tier1_id) {
            const tier1Data = locationData.find((loc: any) => loc.id === tier1_id);
            if (tier1Data) tier1 = tier1Data.name;

            if (tier2_id) {
              const tier2Data = tier1Data.locationTwos.find(
                (loc: any) => loc.id === tier2_id
              );
              if (tier2Data) {
                tier2 = tier2Data.name;

                if (tier3_id) {
                  const tier3Data = tier2Data.locationThrees.find(
                    (loc: any) => loc.id === tier3_id
                  );
                  if (tier3Data) tier3 = tier3Data.name;
                }
              }
            }
          }

          return hierarchicalData.map((x: any) => ({
            ...x,
            ...{
              uniqueEfId:
                `S${efStandardId}-${x?.dateId ? "T" + x.dateId : "NA"
                }-G${efGhgCatId}-GS${efGhgSubCatId}-I${efCategoryId}-` +
                x.hierarchyId, uniqueId: x.hierarchyId,
              startMonth: x?.startDate || "NA",
              endMonth: x?.endDate || "NA",
              methodology: x?.methodology || "Not Found",
              tier1_id,
              tier2_id,
              tier3_id,
              tier1,
              tier2,
              tier3,
              standard: efStandard?.title || "Not Found",
              ghgcategory: efGhgCat?.title || "Not Found",
              ghgsubcategory: efGhgSubCat?.title || "Not Found",
              itemId: efCategory?.id || "",
              item: efCategory?.title || "Not Found",
              co2e: x?.co2e || "-",
              co2: x?.co2 || "-",
              ch4: x?.ch4 || "-",
              n2o: x?.n2o || "-",
            },
          }));
        }
      )
      .reduce((a, b) => [...a, ...b], []);
  }
  getValueByPercentage(value: any, percent: any) {
    const val = parseFloat(value);
    const pct = parseFloat(percent);

    if (isNaN(val) || isNaN(pct)) return 0;

    return (val * pct) / 100;
  }
  getCoverageText(rowData: any, rawsitelist: any) {
    let text = "";

    if (rowData.level === 0) {
      text = "Corporate";
    } else if (rowData.level === 1) {
      let country_index = rawsitelist.findIndex(
        (i: any) => i.id === rowData.locationId
      );
      if (country_index !== -1) {
        text = rawsitelist[country_index].name;
      }
    } else if (rowData.level === 2) {
      let city_index = rawsitelist
        ?.flatMap((i: any) =>
          i.locationTwos?.flatMap((j: any) =>
            j.locationThrees?.map((k: any) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )
        .findIndex((i: any) => {
          return i.city_id === rowData.locationId;
        });
      if (city_index !== -1) {
        text = rawsitelist.flatMap((i: any) =>
          i.locationTwos.flatMap((j: any) =>
            j.locationThrees.map((k: any) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )[city_index].city_name;
      }
    } else if (rowData.level === 3) {
      let site_index = rawsitelist
        .flatMap((i: any) =>
          i.locationTwos.flatMap((j: any) =>
            j.locationThrees.map((k: any) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )
        .findIndex((i: any) => {
          return i.site_id === rowData.locationId;
        });
      if (site_index !== -1) {
        text = rawsitelist.flatMap((i: any) =>
          i.locationTwos.flatMap((j: any) =>
            j.locationThrees.map((k: any) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )[site_index].site_name;
      }
    }
    return text;
  };
  async processCustomMetrics(customMetricResponse: any, filtered: any[], efassignment: any[], rawsitelist: any[]) {
    const metricArray: any = []

    for (const indi of customMetricResponse) {

      // Process filtered data for each metric response
      await Promise.all(
        filtered
          .filter(x => indi.dcfIds.includes(x?.dcfId) || indi.sapIds.includes(x?.sapId))
          .map(async (i) => {
            const index = metricArray.findIndex((y: any) => y.indicatorId === indi.id);

            if (i.formCategory || i.sapId) {


              // i.methodology = indi.type === 1 ? '-' : indi.standalone_ids.map(i => "MT" + i).join(' + ')
              Object.assign(i, {indicatorTitle: indi.title, indicatorId: indi.id, indicatorUnit: indi?.data1?.[0]?.unit || '-', indicatorType: indi.standalone_ids.length === 1 && indi.standalone_ids.includes(indi.id) ? 1 : indi.standalone_ids.length > 1 ? 2 : 0})
              if (i.isNull) {
                Object.assign(i, {value: 0})
              }
              if (i.formType === 2 && i.dataType === 1) {

                const lastDate = DateTime.fromFormat(i.reporting_period.split(' to ')[0], 'LLL-yyyy', {zone: 'utc'});
                let filteredAssignment1 = []
                if (indi.id === 172) {
                  const titleParts = i.actualTitle.split('>').map((s: any) => s.trim().toLowerCase());
                  filteredAssignment1 = efassignment.filter(x => x.itemId === 16 || x.itemId === 58).filter(x => {

                    const match1 = (x.subCategory1 || '')?.trim().toLowerCase().includes(titleParts[0]);
                    const match2 = (x.subCategory2 || '')?.trim().toLowerCase().includes(titleParts[1]);
                    const match3 = (x.subCategory3 || '')?.trim().toLowerCase().includes(titleParts[2]);

                    const titleMatch =
                      titleParts.length === 3 ? match1 && match2 && match3 :
                        titleParts.length === 2 ? match1 && match2 :
                          titleParts.length === 1 ? match1 : false;

                    return titleMatch && x.startMonth && x.startMonth !== 'NA';
                  });


                } else {
                  filteredAssignment1 = efassignment.filter(
                    x => (x.hierarchyId === String(i.uniqueId)) && x.startMonth && x.startMonth !== 'NA'
                  );
                }

                const dateIndex = filteredAssignment1.findIndex((dateRange) => {
                  const startDate = DateTime.fromFormat(dateRange.startMonth, 'LLL-yyyy');
                  const endDate = (dateRange.endMonth && dateRange.endMonth !== "Present")
                    ? DateTime.fromFormat(dateRange.endMonth, 'LLL-yyyy')
                    : DateTime.local();

                  // Check if reporting_date falls within the range
                  return lastDate >= startDate && lastDate <= endDate;
                });

                if (dateIndex !== -1) {
                  const filteredLocation = filterDataByTierAndLocationByLevel(
                    [{locationId: i.locationId, level: i.level}],
                    rawsitelist,
                    filteredAssignment1[dateIndex].tier1_id,
                    filteredAssignment1[dateIndex].tier2_id,
                    filteredAssignment1[dateIndex].tier3_id
                  );

                  if (filteredLocation.length > 0) {
                    if (indi.id === 481) {

                      const obj = filteredAssignment1[dateIndex];
                      const props = this.fuelProperties[obj.uniqueEfId] || {NCV: null, Density: null};

                      Object.assign(i, {
                        emissionFactorName: '-',
                        emissionFactorValue: '-',
                        emissionFactorCo2Value: '-',
                        emissionFactorCh4Value: '-',
                        emissionFactorN2oValue: '-',
                        'Net Calorific value (GJ/Tonne)': props.NCV,
                        'Density (kg/l)': props.Density,
                        efkey: '-',
                        methodology: props.Formula,
                        computedValue: (i.dcfId === 257) ? (i.value * (3.6 / 1000)) : (i.dcfId === 11) ? props.Density ? (parseFloat(((i.value * props.Density) / 1000).toFixed(3)) * props.NCV) : props.NCV ? (i.value * props.NCV) : '-' : '-',
                        computedCo2Value: '-',
                        computedCh4Value: '-',
                        computedN2oValue: '-'

                      });
                    } else {
                      const obj = filteredAssignment1[dateIndex];
                      Object.assign(i, {
                        emissionFactorName: obj.standard,
                        emissionFactorValue: obj.co2e,
                        emissionFactorCo2Value: obj.co2,
                        emissionFactorCh4Value: obj.ch4,
                        emissionFactorN2oValue: obj.n2o,
                        efkey: obj.uniqueEfId,
                        methodology: (indi.id === 172 && i.dcfId === 257) ? "Electricity Consumption * 17.68 % * Emission Factor / 1000 " : i.dcfId === 10 ? '(Quantity of Refrigerant Refilled * Emission Factor)/1000' : i.dcfId === 15 ? '1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000' : i.dcfId === 257 ? 'Electricity Consumption* Emission Factor' : obj.methodology,
                        computedValue: (indi.id === 172 && i.dcfId === 257) ? ((obj.co2e / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.co2e / 1000) * i.value).toFixed(3),
                        computedCo2Value: (indi.id === 172 && i.dcfId === 257) ? ((obj.co2 / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.co2 / 1000) * i.value).toFixed(3),
                        computedCh4Value: (indi.id === 172 && i.dcfId === 257) ? ((obj.ch4 / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.ch4 / 1000) * i.value).toFixed(3),
                        computedN2oValue: (indi.id === 172 && i.dcfId === 257) ? ((obj.n2o / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.n2o / 1000) * i.value).toFixed(3),

                      });
                    }

                  }
                } else if (i?.efValue) {

                  Object.assign(i, {
                    emissionFactorName: '-',
                    efkey: '-',
                    formula: '-',
                    computedValue: i?.efValue,
                  })
                } else {

                  // Set default values if no valid date range is found
                  Object.assign(i, {
                    dateIndex,
                    emissionFactorName: '-',
                    emissionFactorValue: '-',
                    efkey: "-",
                    formula: "-",
                    computedValue: "-",
                  });
                }
                // Push once for formType === 2 && dataType === 1
                metricArray.push(i);
              } else if (i.formType === 1 && i.dataType === 1) {
                // Push once for formType === 1 && dataType === 1
                metricArray.push({
                  ...i,
                  emissionFactorName: 'NA',
                  emissionFactorValue: 'NA',
                  emissionFactorCo2Value: 'NA',
                  emissionFactorCh4Value: 'NA',
                  emissionFactorN2oValue: 'NA',
                  efkey: 'NA',
                  methodology: 'NA',
                  computedValue: i.value,
                  computedCo2Value: 'NA',
                  computedCh4Value: 'NA',
                  computedN2oValue: 'NA',
                })
              } else if (i.formType === 2 && i.dataType === 2) {
                // Push once for formType === 2 && dataType === 2
                metricArray.push({
                  ...i,
                  emissionFactorName: 'NA',
                  emissionFactorValue: 'NA',
                  emissionFactorCo2Value: 'NA',
                  emissionFactorCh4Value: 'NA',
                  emissionFactorN2oValue: 'NA',
                  efkey: 'NA',
                  methodology: 'NA',
                  computedValue: i?.conversionValue || i?.value || null,
                  computedCo2Value: 'NA',
                  computedCh4Value: 'NA',
                  computedN2oValue: 'NA',
                })
              } else if (i.efKey) {
                // Handle efKey case
                const lastDate = DateTime.fromFormat(i.reporting_period.split(' to ')[0], 'LLL-yyyy', {zone: 'utc'});
                const filteredAssignment1 = efassignment.filter(
                  x => (x.hierarchyId === i.efKey)
                );

                const dateIndex = filteredAssignment1.findIndex((dateRange) => {
                  const startDate = DateTime.fromFormat(dateRange.startMonth, 'LLL-yyyy');
                  const endDate = (dateRange.endMonth && dateRange.endMonth !== "Present")
                    ? DateTime.fromFormat(dateRange.endMonth, 'LLL-yyyy')
                    : DateTime.local();

                  // Check if reporting_date falls within the range
                  return lastDate >= startDate && lastDate <= endDate;
                });

                if (dateIndex !== -1) {
                  const filteredLocation = filterDataByTierAndLocationByLevel(
                    [{locationId: i.locationId, level: i.level}],
                    rawsitelist,
                    filteredAssignment1[dateIndex].tier1_id,
                    filteredAssignment1[dateIndex].tier2_id,
                    filteredAssignment1[dateIndex].tier3_id
                  );

                  if (filteredLocation.length > 0) {
                    const obj = filteredAssignment1[dateIndex];
                    Object.assign(i, {
                      value: indi.id === 1642 ? this.getValueByPercentage(i.value, 2) : i.value,
                      emissionFactorName: obj.standard,
                      emissionFactorValue: obj.co2e,
                      emissionFactorCo2Value: obj.co2,
                      emissionFactorCh4Value: obj.ch4,
                      emissionFactorN2oValue: obj.n2o,
                      efkey: obj.uniqueEfId,
                      methodology: i.dcfId === 10 ? '(Quantity of Refrigerant Refilled * Emission Factor)/1000' : i.dcfId === 15 ? '1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000' : i.dcfId === 257 ? 'Electricity Consumption* Emission Factor' : obj.methodology,
                      computedValue: indi.id === 1642 ? ((obj.co2e / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.co2e / 1000) * i.value).toFixed(3),
                      computedCo2Value: indi.id === 1642 ? ((obj.co2 / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.co2 / 1000) * i.value).toFixed(3),
                      computedCh4Value: indi.id === 1642 ? ((obj.ch4 / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.ch4 / 1000) * i.value).toFixed(3),
                      computedN2oValue: indi.id === 1642 ? ((obj.n2o / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.n2o / 1000) * i.value).toFixed(3),
                    });
                  }
                } else {
                  // Set default values if no valid date range is found
                  Object.assign(i, {
                    emissionFactorName: '-',
                    emissionFactorValue: '-',
                    efkey: "-",
                    formula: "-",
                    computedValue: "-",
                  });
                }
                // Push once for efKey case
                metricArray.push(i);
              } else {

                if ((indi.id === 695 || indi.id === 693)) {
                  if (i.indicatorId_ === indi.id) {
                    Object.assign(i, {
                      emissionFactorName: i?.emissionFactorName || '-',
                      efkey: i?.efKey || '-',
                      formula: i?.methodology || '-',
                      computedValue: i?.efValue,
                    })


                  } else {
                    return;
                  }

                } else if (i?.efValue) {

                  Object.assign(i, {
                    emissionFactorName: i?.emissionFactorName || '-',
                    efkey: i?.efKey || '-',
                    formula: i?.methodology || '-',
                    computedValue: i?.efValue,
                  })

                } else {
                  i.computedValue = i.value


                }

                metricArray.push(i);
                // Push once for other cases

              }
            } else {
              metricArray.push({
                ...i, value: 'NA', indicatorUnit: indi?.data1?.[0]?.unit || '-', indicatorTitle: indi.title, indicatorId: indi.id, indicatorType: indi.standalone_ids.length === 1 && indi.standalone_ids.includes(indi.id) ? 1 : indi.standalone_ids.length > 1 ? 2 : 0,
                emissionFactorName: 'NA',
                emissionFactorValue: 'NA',
                emissionFactorCo2Value: 'NA',
                emissionFactorCh4Value: 'NA',
                emissionFactorN2oValue: 'NA',
                efkey: 'NA',
                methodology: 'NA',
                computedValue: 'NA',
                computedCo2Value: 'NA',
                computedCh4Value: 'NA',
                computedN2oValue: 'NA',
              })
            }
            // Update metricArray
            // if (index === -1) {
            //   // Add new indicator
            //   metricArray.push({
            //     indicatorId: indi.id,
            //     frameworkTags: indi.overallTags, title: indi.title, type: indi?.type || null, standalone_ids: indi?.standalone_ids, unit: indi?.data1?.[0]?.unit || '-',
            //     contributingEntities: [{
            //       title: i.entity,
            //       locationId: i.locationId,
            //       level: i.level,
            //       contributingReportingPeriod: [{
            //         title: i.reporting_period,
            //         status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
            //         contributingDataPoints: [i],
            //       }]
            //     }]
            //   });
            // } else {
            //   // Update existing indicator
            //   const entityIndex = metricArray[index].contributingEntities.findIndex(
            //     (y: any) => y.locationId === i.locationId && y.level === i.level
            //   );

            //   if (entityIndex === -1) {
            //     // Add new contributing entity
            //     metricArray[index].contributingEntities.push({
            //       title: i.entity,
            //       locationId: i.locationId,
            //       level: i.level,
            //       contributingReportingPeriod: [{
            //         title: i.reporting_period,
            //         status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
            //         contributingDataPoints: [i],
            //       }]
            //     });
            //   } else {

            //     // Update existing contributing entity
            //     const periodIndex = metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod.findIndex(
            //       (y: any) => y.title === i.reporting_period
            //     );

            //     if (periodIndex === -1) {
            //       // Add new reporting period
            //       metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod.push({
            //         title: i.reporting_period,
            //         status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
            //         contributingDataPoints: [i],
            //       });
            //     } else {
            //       // Add data point to existing reporting period
            //       metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod[periodIndex].contributingDataPoints.push(i);
            //     }
            //   }
            // }
          })
      );
    }
    return metricArray
  }

  @post('/user-profiles/{clientId}/get-locations-by-roles')
  async getFilteredLocations(
    @param.path.number('clientId') clientId: number,
    @requestBody({
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {
                type: 'number',
                description: 'User ID for role-based location filtering'
              },
              roles: {
                type: 'array',
                items: {type: 'number'},
                description: 'Array of role IDs to filter by'
              }
            },
            required: ['userId', 'roles']
          },
        },
      },
    })
    requestBody: {
      userId: number;
      roles: number[];
    }
  ): Promise<any> {
    const {userId, roles} = requestBody;

    // Get user's role assignments for the specified roles
    let userRoleLocations: any[] = [];
    let hasGlobalAccess = false;

    // Check if userId and clientId are the same (Global Level Access)
    if (userId === clientId) {
      hasGlobalAccess = true;
    } else {
      // Build SQL query based on the reference endpoint approach
      let sqlQuery = `
        SELECT * FROM UserRoleAuthorization
        WHERE user_id = ?
          AND userProfileId = ?
          AND roles IS NOT NULL
          AND JSON_LENGTH(roles) > 0
      `;

      const queryParams: any[] = [userId, clientId];

      console.log('=== ROLE FILTERING DEBUG ===');
      console.log('userId:', userId, 'clientId:', clientId, 'roles:', roles);

      // Add role filtering using the same approach as filter-by-roles endpoint
      if (roles.length === 1) {
        // Single roleId - simple JSON_CONTAINS
        sqlQuery += ` AND JSON_CONTAINS(roles, ?, '$')`;
        queryParams.push(JSON.stringify([roles[0]]));
      } else {
        // Multiple roleIds - use OR logic to match any of the roles
        const roleConditions = roles.map(() => `JSON_CONTAINS(roles, ?, '$')`).join(' OR ');
        sqlQuery += ` AND (${roleConditions})`;

        // Add each roleId as a parameter
        for (const roleId of roles) {
          queryParams.push(JSON.stringify([roleId]));
        }
      }

      console.log('SQL Query:', sqlQuery);
      console.log('Query Params:', queryParams);

      const roleResults = await this.userRoleAuthorizationRepository.execute(sqlQuery, queryParams);
      const rawRoleLocations = Array.isArray(roleResults) ? roleResults : [];

      // Parse the roles property from JSON string to array (same as reference endpoint)
      userRoleLocations = rawRoleLocations.map((record: any) => {
        if (record && record.roles) {
          try {
            // Parse roles if it's a string, otherwise keep as is
            record.roles = typeof record.roles === 'string' ? JSON.parse(record.roles) : record.roles;
          } catch (error) {
            // If parsing fails, keep the original value
            console.warn('Failed to parse roles JSON:', record.roles);
          }
        }
        return record;
      });

      console.log('Raw role assignments found:', userRoleLocations.length);
      console.log('Parsed role assignments:', JSON.stringify(userRoleLocations, null, 2));
    }

    // Check if user has corporate level assignment with specified roles
    const hasCorporateAssignment = userRoleLocations.some(assignment => {
      const isCorporateLevel = assignment.tier1_id === 0 &&
        assignment.tier2_id === null &&
        assignment.tier3_id === null;

      if (!isCorporateLevel) return false;

      // Check if this corporate assignment has any of the requested roles
      if (assignment.roles && Array.isArray(assignment.roles)) {
        const hasRequestedRole = assignment.roles.some((role: number) => roles.includes(role));
        console.log('Corporate assignment roles:', assignment.roles, 'requested roles:', roles, 'has match:', hasRequestedRole);
        return hasRequestedRole;
      }

      return false;
    });

    console.log('hasGlobalAccess:', hasGlobalAccess);
    console.log('hasCorporateAssignment:', hasCorporateAssignment);

    // Get all locations for the client
    const allLocations = await this.userProfileRepository.locationOnes(clientId).find({
      include: [
        {
          relation: "locationTwos",
          scope: {
            include: [{relation: "locationThrees"}],
          },
        },
      ],
    });

    // Filter locations based on role assignments
    let filteredLocations = allLocations;

    console.log('Total locations before filtering:', allLocations.length);

    if (userId && userRoleLocations.length > 0) {
      if (hasGlobalAccess || hasCorporateAssignment) {
        // Global access or corporate assignment: user can see all locations
        console.log('Using global/corporate access - showing all locations');
        filteredLocations = allLocations;
      } else {
        // Filter locations based on role assignments (includes assigned + all child levels)
        console.log('Filtering based on role assignments');
        filteredLocations = this.filterAccessibleLocationsByRole3Assignments(allLocations, userRoleLocations);
      }
    }

    console.log('Locations after role filtering:', filteredLocations.length);

    // Apply the filtering to ensure all locations have level 3 (sites)
    console.log('=== LEVEL 3 FILTERING DEBUG ===');
    console.log('Locations before level 3 filtering:', filteredLocations.length);

    filteredLocations.forEach((location, index) => {
      console.log(`Location ${index}:`, {
        id: location.id,
        name: location.name,
        locationTwosCount: location.locationTwos?.length || 0
      });

      if (location.locationTwos) {
        location.locationTwos.forEach((city: any, cityIndex: number) => {
          console.log(`  City ${cityIndex}:`, {
            id: city.id,
            name: city.name,
            locationThreesCount: city.locationThrees?.length || 0
          });
        });
      }
    });

    const shapedLocations = filteredLocations
      .map((item) => {
        const originalCityCount = item.locationTwos?.length || 0;

        if (item.locationTwos) {
          item.locationTwos = item.locationTwos.filter(
            (locationTwo) =>
              locationTwo.locationThrees &&
              locationTwo.locationThrees.length > 0
          );
        }

        const filteredCityCount = item.locationTwos?.length || 0;
        console.log(`Country ${item.name}: ${originalCityCount} cities → ${filteredCityCount} cities with sites`);

        return item;
      })
      .filter((item) => item.locationTwos && item.locationTwos.length > 0);

    console.log('Locations after level 3 filtering:', shapedLocations.length);
    console.log('=== END LEVEL 3 FILTERING DEBUG ===');

    return shapedLocations


  }

  /**
   * Filter locations based on user's role 3 assignments (includes assigned + all child levels)
   * This method returns ACCESSIBLE entities (broader than just assigned entities)
   * @param allLocations All available locations
   * @param userRole3Locations User's role 3 location assignments
   * @returns Filtered locations based on role 3 assignments + child levels
   */
  private filterAccessibleLocationsByRole3Assignments(allLocations: any[], userRole3Locations: any[]): any[] {
    if (!userRole3Locations || userRole3Locations.length === 0) {
      return allLocations;
    }

    // Parse role assignments to ensure proper data types
    const parsedAssignments = userRole3Locations.map(assignment => ({
      ...assignment,
      tier1_id: parseInt(assignment.tier1_id),
      tier2_id: assignment.tier2_id === null ? null : parseInt(assignment.tier2_id),
      tier3_id: assignment.tier3_id === null ? null : parseInt(assignment.tier3_id),
    }));

    console.log('Parsed assignments for location filtering:', JSON.stringify(parsedAssignments, null, 2));

    const accessibleLocations: any[] = [];

    for (const location of allLocations) {
      // Check if this location (tier1) is accessible by the user
      const hasAccessToLocation = parsedAssignments.some(assignment => {
        // Corporate level access (tier1_id = 0) - can access everything
        if (assignment.tier1_id === 0 && assignment.tier2_id === null && assignment.tier3_id === null) {
          return true;
        }
        // Country level access - can access this country and all its children
        if (assignment.tier1_id === location.id && assignment.tier2_id === 0 && assignment.tier3_id === null) {
          return true;
        }
        // Region/City level access - can access this country if it contains assigned regions
        if (assignment.tier1_id === location.id && assignment.tier2_id !== 0 && assignment.tier3_id === 0) {
          return true; // User can see the country and all its children
        }
        // Site level access - can access this country if it contains assigned sites
        if (assignment.tier1_id === location.id && assignment.tier2_id !== 0 && assignment.tier3_id !== 0) {
          return true; // User can see the country and all its children
        }
        return false;
      });

      if (hasAccessToLocation) {
        // For accessible entities, we include the full location structure
        // but we need to determine what the user can actually see
        const accessibleLocation = {...location};

        if (location.locationTwos) {
          // Include all cities/regions that are accessible
          accessibleLocation.locationTwos = location.locationTwos.filter((tier2: any) => {
            return parsedAssignments.some(assignment => {
              // Corporate or country level access includes all children
              if ((assignment.tier1_id === 0) ||
                (assignment.tier1_id === location.id && assignment.tier2_id === 0)) {
                return true;
              }
              // Region level access - include this region and all its children
              if (assignment.tier1_id === location.id && assignment.tier2_id === tier2.id && assignment.tier3_id === 0) {
                return true;
              }
              // Site level access - include this region if it contains assigned sites
              if (assignment.tier1_id === location.id && assignment.tier2_id === tier2.id && assignment.tier3_id !== 0) {
                return true;
              }
              return false;
            });
          }).map((tier2: any) => {
            const accessibleTier2 = {...tier2};
            if (tier2.locationThrees) {
              // Include all sites that are accessible
              accessibleTier2.locationThrees = tier2.locationThrees.filter((tier3: any) => {
                return parsedAssignments.some(assignment => {
                  // Corporate, country, or region level access includes all children
                  if ((assignment.tier1_id === 0) ||
                    (assignment.tier1_id === location.id && assignment.tier2_id === 0) ||
                    (assignment.tier1_id === location.id && assignment.tier2_id === tier2.id && assignment.tier3_id === 0)) {
                    return true;
                  }
                  // Site level access - include this specific site
                  return assignment.tier1_id === location.id &&
                    assignment.tier2_id === tier2.id &&
                    assignment.tier3_id === tier3.id;
                });
              });
            }
            return accessibleTier2;
          });
        }

        accessibleLocations.push(accessibleLocation);
      }
    }

    return accessibleLocations;
  }

  /**
   * Filter locations based on user's role 3 assignments (EXACT assignments only)
   * This method returns only ASSIGNED entities (strict assignment matching)
   * @param allLocations All available locations
   * @param userRole3Locations User's role 3 location assignments
   * @returns Filtered locations based on exact role 3 assignments
   */
  private filterLocationsByRole3Assignments(allLocations: any[], userRole3Locations: any[]): any[] {
    if (!userRole3Locations || userRole3Locations.length === 0) {
      return allLocations;
    }

    const filteredLocations: any[] = [];

    for (const location of allLocations) {
      // Check if this location (tier1) is accessible by the user
      const hasAccessToLocation = userRole3Locations.some(assignment => {
        // Corporate level access (tier1_id = 0)
        if (assignment.tier1_id === 0 && assignment.tier2_id === null && assignment.tier3_id === null) {
          return true;
        }
        // Country level access
        if (assignment.tier1_id === location.id && assignment.tier2_id === 0 && assignment.tier3_id === null) {
          return true;
        }
        // Region/City level access
        if (assignment.tier1_id === location.id && assignment.tier2_id !== 0 && assignment.tier3_id === 0) {
          return location.locationTwos?.some((tier2: any) => tier2.id === assignment.tier2_id);
        }
        // Site level access
        if (assignment.tier1_id === location.id && assignment.tier2_id !== 0 && assignment.tier3_id !== 0) {
          return location.locationTwos?.some((tier2: any) =>
            tier2.id === assignment.tier2_id &&
            tier2.locationThrees?.some((tier3: any) => tier3.id === assignment.tier3_id)
          );
        }
        return false;
      });

      if (hasAccessToLocation) {
        // Filter the location's children based on assignments
        const filteredLocation = {...location};

        if (location.locationTwos) {
          filteredLocation.locationTwos = location.locationTwos.filter((tier2: any) => {
            return userRole3Locations.some(assignment => {
              // Corporate or country level access includes all children
              if ((assignment.tier1_id === 0) ||
                (assignment.tier1_id === location.id && assignment.tier2_id === 0)) {
                return true;
              }
              // Region level access
              if (assignment.tier1_id === location.id && assignment.tier2_id === tier2.id && assignment.tier3_id === 0) {
                return true;
              }
              // Site level access
              if (assignment.tier1_id === location.id && assignment.tier2_id === tier2.id && assignment.tier3_id !== 0) {
                return tier2.locationThrees?.some((tier3: any) => tier3.id === assignment.tier3_id);
              }
              return false;
            });
          }).map((tier2: any) => {
            const filteredTier2 = {...tier2};
            if (tier2.locationThrees) {
              filteredTier2.locationThrees = tier2.locationThrees.filter((tier3: any) => {
                return userRole3Locations.some(assignment => {
                  // Corporate, country, or region level access includes all children
                  if ((assignment.tier1_id === 0) ||
                    (assignment.tier1_id === location.id && assignment.tier2_id === 0) ||
                    (assignment.tier1_id === location.id && assignment.tier2_id === tier2.id && assignment.tier3_id === 0)) {
                    return true;
                  }
                  // Site level access
                  return assignment.tier1_id === location.id &&
                    assignment.tier2_id === tier2.id &&
                    assignment.tier3_id === tier3.id;
                });
              });
            }
            return filteredTier2;
          });
        }

        filteredLocations.push(filteredLocation);
      }
    }

    return filteredLocations;
  }

  /**
   * Apply specific location filter (Country, City/Region, State/Site)
   * @param locations Locations to filter
   * @param locationFilter Specific location filter criteria
   * @param userRole3Locations User's role 3 assignments (for tier1_id: 0 filtering)
   * @param hasGlobalAccess Whether user has global/corporate access
   * Special values:
   * - tier1_id: 0 = Show ALL assigned entities as approver
   * - tier2_id: 0 = Country level (all cities in specified country)
   * - tier3_id: null = Region level (all sites in specified region)
   * @returns Filtered locations based on specific criteria
   */
  private applySpecificLocationFilter(
    locations: any[],
    locationFilter: {
      tier1_id?: number | null;
      tier2_id?: number | null;
      tier3_id?: number | null;
    },
    userRole3Locations?: any[],
    hasGlobalAccess?: boolean
  ): any[] {
    if (!locationFilter) {
      return locations;
    }

    // tier1_id = 0 means show ALL assigned entities as approver AND their children
    if (locationFilter.tier1_id === 0) {
      if (hasGlobalAccess) {
        // Global access: return all locations
        return locations;
      } else if (userRole3Locations && userRole3Locations.length > 0) {
        // Return assigned locations AND their accessible children (hierarchical access)
        return this.filterAccessibleLocationsByRole3Assignments(locations, userRole3Locations);
      } else {
        // No assignments, return empty
        return [];
      }
    }

    return locations.filter(location => {
      // Filter by specific Country (tier1)
      if (locationFilter.tier1_id !== undefined && locationFilter.tier1_id !== null && locationFilter.tier1_id !== 0) {
        if (location.id !== locationFilter.tier1_id) {
          return false;
        }
      }

      // Country level: tier2_id = 0 means show all cities in the country
      if (locationFilter.tier2_id === 0) {
        // Keep all cities in this country, don't filter tier2
        return true;
      }

      // Filter by specific City/Region (tier2)
      if (locationFilter.tier2_id !== undefined && locationFilter.tier2_id !== null && locationFilter.tier2_id !== 0) {
        if (!location.locationTwos?.some((tier2: any) => tier2.id === locationFilter.tier2_id)) {
          return false;
        }
        // Filter the locationTwos array to only include the specified tier2
        location.locationTwos = location.locationTwos.filter((tier2: any) => tier2.id === locationFilter.tier2_id);
      }

      // Region level: tier3_id = null means show all sites in the region
      if (locationFilter.tier3_id === null) {
        // Keep all sites in the specified regions, don't filter tier3
        return true;
      }

      // Filter by specific State/Site (tier3)
      if (locationFilter.tier3_id !== undefined && locationFilter.tier3_id !== null) {
        if (location.locationTwos) {
          location.locationTwos = location.locationTwos.map((tier2: any) => {
            if (tier2.locationThrees) {
              tier2.locationThrees = tier2.locationThrees.filter((tier3: any) => tier3.id === locationFilter.tier3_id);
            }
            return tier2;
          }).filter((tier2: any) => tier2.locationThrees && tier2.locationThrees.length > 0);
        }

        // If no matching tier3 found, exclude this location
        if (!location.locationTwos || location.locationTwos.length === 0) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Filter SAP data based on location filtering
   * @param sapData SAP data to filter
   * @param filteredLocations Filtered locations based on user access
   * @returns Filtered SAP data
   */
  private filterSapDataByLocation(sapData: any[], filteredLocations: any[]): any[] {
    if (!sapData || sapData.length === 0 || !filteredLocations || filteredLocations.length === 0) {
      return sapData || [];
    }

    // Extract all accessible location names from the filtered locations
    const accessibleLocationNames = new Set<string>();

    filteredLocations.forEach(country => {
      accessibleLocationNames.add(country.name);

      if (country.locationTwos) {
        country.locationTwos.forEach((city: any) => {
          accessibleLocationNames.add(city.name);

          if (city.locationThrees) {
            city.locationThrees.forEach((site: any) => {
              accessibleLocationNames.add(site.name);
            });
          }
        });
      }
    });

    // Filter SAP data based on Plant/entity field
    return sapData.filter(item => {
      const entityName = item.entity;
      if (!entityName || entityName === 'NA') {
        return false; // Exclude items with no entity or 'NA'
      }

      // Check if the entity name matches any accessible location
      return accessibleLocationNames.has(entityName);
    });
  }

  /**
   * Filter HR data based on location filtering
   * @param hrData HR data to filter
   * @param filteredLocations Filtered locations based on user access
   * @returns Filtered HR data
   */
  private filterHRDataByLocation(hrData: any[], filteredLocations: any[]): any[] {
    if (!hrData || hrData.length === 0 || !filteredLocations || filteredLocations.length === 0) {
      return hrData || [];
    }

    // Extract all accessible location names from the filtered locations
    const accessibleLocationNames = new Set<string>();

    filteredLocations.forEach(country => {
      accessibleLocationNames.add(country.name);

      if (country.locationTwos) {
        country.locationTwos.forEach((city: any) => {
          accessibleLocationNames.add(city.name);

          if (city.locationThrees) {
            city.locationThrees.forEach((site: any) => {
              accessibleLocationNames.add(site.name);
            });
          }
        });
      }
    });

    // Filter HR data based on entity field (assuming similar structure to SAP)
    return hrData.filter(item => {
      const entityName = item.entity;
      if (!entityName || entityName === 'NA') {
        return false; // Exclude items with no entity or 'NA'
      }

      // Check if the entity name matches any accessible location
      return accessibleLocationNames.has(entityName);
    });
  }

  /**
   * Get valid tier IDs based on location filter
   */
  private getValidTierIds(locationData: any[], tier1_id?: number | null, tier2_id?: number | null, tier3_id?: number | null) {
    const countries = new Set<number>();
    const regions = new Set<number>();
    const businessUnits = new Set<number>();
    console.log(tier1_id, tier2_id, tier3_id, locationData)
    locationData.forEach(country => {
      if (tier1_id === 0 || tier1_id === country.id) {
        countries.add(country.id);
        console.log('1')
        if (country.locationTwos) {
          country.locationTwos.forEach((region: any) => {
            if (tier2_id === 0 || tier2_id === region.id) {
              regions.add(region.id);
              console.log('2')
              if (region.locationThrees) {
                region.locationThrees.forEach((businessUnit: any) => {
                  if (tier3_id === 0 || (tier2_id === 0 && tier3_id === null) || tier3_id === businessUnit.id) {
                    console.log('3')
                    businessUnits.add(businessUnit.id);
                  }
                });
              }
            }
          });
        }
      }
    });

    return {
      countries: Array.from(countries),
      regions: Array.from(regions),
      businessUnits: Array.from(businessUnits)
    };
  }

  /**
   * Filter data based on specific location filter (tier1_id, tier2_id, tier3_id)
   * This method filters data to show ONLY the specified location and its direct children,
   * regardless of role assignments or user access restrictions.
   * @param data Data array to filter (result, SAP, HR data)
   * @param locationFilter Specific location filter criteria
   * @param filteredLocations Used to determine the actual location hierarchy
   * @returns Filtered data matching the specific location criteria
   */
  private filterDataBySpecificLocation(
    data: any[],
    locationFilter: {
      tier1_id?: number | null;
      tier2_id?: number | null;
      tier3_id?: number | null;
    },
    filteredLocations?: any[]
  ): any[] {
    if (!data || data.length === 0) {
      return data || [];
    }

    if (!locationFilter || !filteredLocations) {
      return data;
    }

    const tier1_id = locationFilter.tier1_id;
    const tier2_id = locationFilter.tier2_id;
    const tier3_id = locationFilter.tier3_id;

    console.log(tier1_id, tier2_id, tier3_id);

    if (tier1_id === 0 && tier2_id === null && tier3_id === null) {
      return data; // If tier is 0, null, null return the given data
    }

    const {countries, regions, businessUnits} = this.getValidTierIds(filteredLocations, tier1_id, tier2_id, tier3_id);
    console.log(countries, regions, businessUnits);

    return data.filter(item => {
      if (tier1_id !== 0 && tier2_id === 0 && tier3_id === null) {
        // Case when we want all regions and sites under a country
        return (item.level === 1 && countries.includes(item.locationId)) ||
          (item.level === 2 && regions.includes(item.locationId)) ||
          (item.level === 3 && businessUnits.includes(item.locationId));
      } else if (tier1_id !== 0 && tier2_id !== 0 && tier3_id === 0) {
        // Case when we want a specific region and all its sites
        return (item.level === 2 && regions.includes(item.locationId)) ||
          (item.level === 3 && businessUnits.includes(item.locationId));
      } else if (tier1_id !== 0 && tier2_id !== 0 && tier3_id !== 0) {
        // Case when we want a specific site
        return item.level === 3 && businessUnits.includes(item.locationId);
      } else {
        // Case when we want only the specific country
        return item.level === 1 && countries.includes(item.locationId);
      }
    });
  }

  /**
   * Filter data based on level and locationId to match user's role assignments
   * Includes assigned locations and their child levels (hierarchical access)
   * @param data Data array to filter (result, SAP, HR data)
   * @param userRole2Locations User's role 2 assignments
   * @returns Filtered data matching assigned locations and their children
   */
  private filterDataByLevelAndLocationId(data: any[], userRole2Locations: any[], filteredLocations?: any[]): any[] {
    if (!data || data.length === 0) {
      return data || [];
    }

    // If no role assignments, return empty
    if (!userRole2Locations || userRole2Locations.length === 0) {
      return [];
    }

    // Parse role assignments and handle string/number conversion
    const parsedAssignments = userRole2Locations.map(assignment => ({
      ...assignment,
      tier1_id: parseInt(assignment.tier1_id),
      tier2_id: assignment.tier2_id === null ? null : parseInt(assignment.tier2_id),
      tier3_id: assignment.tier3_id === null ? null : parseInt(assignment.tier3_id),
      roles: Array.isArray(assignment.roles) ? assignment.roles :
        (typeof assignment.roles === 'string' ? JSON.parse(assignment.roles) : [])
    }));


    // Create sets of accessible location IDs based on assignments (including children)
    const accessibleLocationIds = new Map<number, Set<number>>();
    accessibleLocationIds.set(0, new Set()); // Corporate
    accessibleLocationIds.set(1, new Set()); // Countries
    accessibleLocationIds.set(2, new Set()); // Cities
    accessibleLocationIds.set(3, new Set()); // Sites

    parsedAssignments.forEach(assignment => {
      // Only process assignments with role 2
      if (!assignment.roles.includes(2)) {
        return;
      }

      // Corporate assignment (tier1_id = 0)
      if (assignment.tier1_id === 0 && assignment.tier2_id === null && assignment.tier3_id === null) {
        // Corporate access - add all levels including corporate level (level 0)
        accessibleLocationIds.get(0)?.add(0); // Corporate level

        // Add all countries, cities, and sites
        if (filteredLocations) {
          filteredLocations.forEach(country => {
            // Add all countries
            accessibleLocationIds.get(1)?.add(country.id);

            if (country.locationTwos) {
              country.locationTwos.forEach((city: any) => {
                // Add all cities
                accessibleLocationIds.get(2)?.add(city.id);

                if (city.locationThrees) {
                  city.locationThrees.forEach((site: any) => {
                    // Add all sites
                    accessibleLocationIds.get(3)?.add(site.id);
                  });
                }
              });
            }
          });
        }
        return;
      }

      // Country assignment (tier2_id = 0, tier3_id = null)
      if (assignment.tier1_id !== 0 && assignment.tier2_id === 0 && assignment.tier3_id === null) {
        // Add the country itself
        accessibleLocationIds.get(1)?.add(assignment.tier1_id);

        // Add all cities and sites under this country using filteredLocations
        if (filteredLocations) {
          filteredLocations.forEach(country => {
            if (country.id === assignment.tier1_id && country.locationTwos) {
              country.locationTwos.forEach((city: any) => {
                // Add all cities under this country
                accessibleLocationIds.get(2)?.add(city.id);

                // Add all sites under each city
                if (city.locationThrees) {
                  city.locationThrees.forEach((site: any) => {
                    accessibleLocationIds.get(3)?.add(site.id);
                  });
                }
              });
            }
          });
        }
        return;
      }

      // City assignment (tier3_id = 0) - Your case: Mysore
      if (assignment.tier1_id !== 0 && assignment.tier2_id !== 0 && assignment.tier3_id === 0) {
        // Add the city itself
        accessibleLocationIds.get(2)?.add(assignment.tier2_id);

        // For city assignment, also include all sites under this city
        if (filteredLocations) {
          filteredLocations.forEach(country => {
            if (country.id === assignment.tier1_id && country.locationTwos) {
              country.locationTwos.forEach((city: any) => {
                if (city.id === assignment.tier2_id && city.locationThrees) {
                  city.locationThrees.forEach((site: any) => {
                    if (site.id) {
                      accessibleLocationIds.get(3)?.add(site.id);
                    }
                  });
                }
              });
            }
          });
        }

        // IMPORTANT: For city assignment, do NOT include country level (level 1) or corporate level (level 0)
        // Only include city level (level 2) and site level (level 3)
        return;
      }

      // Site assignment (specific tier3_id)
      if (assignment.tier1_id !== 0 && assignment.tier2_id !== 0 && assignment.tier3_id !== 0) {
        accessibleLocationIds.get(3)?.add(assignment.tier3_id);
        return;
      }
    });

    console.log('=== ACCESSIBLE LOCATION IDS DEBUG ===');
    console.log('Level 0 (Corporate):', Array.from(accessibleLocationIds.get(0) || []));
    console.log('Level 1 (Countries):', Array.from(accessibleLocationIds.get(1) || []));
    console.log('Level 2 (Cities):', Array.from(accessibleLocationIds.get(2) || []));
    console.log('Level 3 (Sites):', Array.from(accessibleLocationIds.get(3) || []));
    console.log('=== END ACCESSIBLE LOCATION IDS DEBUG ===');

    // Filter data based on level and locationId
    return data.filter(item => {
      const level = item.level;
      const locationId = item.locationId;

      // Handle missing level/locationId (SAP data case)
      // Note: locationId 0 is valid (Corporate level), so don't treat it as missing
      if (level === undefined || level === null ||
        locationId === undefined || locationId === null) {
        // If level or locationId is missing, exclude the item
        console.log(`Excluding item with missing level/locationId: level=${level}, locationId=${locationId}, entity=${item.entity}`);
        return false;
      }

      // Check if the locationId is accessible at the specified level
      const accessibleIds = accessibleLocationIds.get(level);
      if (!accessibleIds) {
        return false;
      }

      const isAccessible = accessibleIds.has(locationId);
      return isAccessible;
    });
  }


}





